const axios = require('axios');
const crypto = require('crypto');

class ReloadlyService {
    constructor(accessToken) {
        this.accessToken = accessToken;
        this.baseUrl = 'https://topups-sandbox.reloadly.com';
        this.httpClient = axios.create({
            headers: {
                'Authorization': `Bearer ${this.accessToken}`,
                'Accept': 'application/com.reloadly.topups-v1+json',
                'Content-Type': 'application/json'
            }
        });
    }

    generateCustomIdentifier() {
        return `tx_${crypto.randomBytes(16).toString('hex')}`;
    }

    async detectOperator(phoneNumber, countryCode) {
        try {
            const url = `${this.baseUrl}/operators/auto-detect/phone/${phoneNumber}/countries/${countryCode}`;
            const response = await this.httpClient.get(url);
            return response.data;
        } catch (error) {
            console.error('Operator detection failed:', error.response?.data || error.message);
            throw error;
        }
    }

    async sendTopUp(operatorId, amount, recipientPhone, countryCode) {
        try {
            const topUpRequest = {
                operatorId,
                amount,
                useLocalAmount: false,
                customIdentifier: this.generateCustomIdentifier(),
                recipientPhone: {
                    countryCode,
                    number: recipientPhone
                }
            };

            const response = await this.httpClient.post(
                `${this.baseUrl}/topups`,
                topUpRequest
            );

            return response.data;
        } catch (error) {
            console.error('Top-up failed:', error.response?.data || error.message);
            throw error;
        }
    }

    async processTopUp(phoneNumber, countryCode, amount) {
        try {
            // Step 1: Detect operator
            console.log(`Detecting operator for phone: ${phoneNumber}, country: ${countryCode}`);
            const operator = await this.detectOperator(phoneNumber, countryCode);
            console.log('Detected operator:', {
                id: operator.operatorId,
                name: operator.name,
                country: operator.country.name
            });

            // Step 2: Send top-up
            console.log(`Sending top-up of ${amount} to ${phoneNumber}`);
            const result = await this.sendTopUp(
                operator.operatorId,
                amount,
                phoneNumber,
                countryCode
            );

            console.log('Top-up successful:', {
                transactionId: result.transactionId,
                status: result.status,
                deliveredAmount: result.deliveredAmount,
                currency: result.deliveredAmountCurrencyCode
            });

            return result;
        } catch (error) {
            console.error('Top-up process failed:', error);
            throw error;
        }
    }
}

// Example usage
(async () => {
    const ACCESS_TOKEN = 'eyJraWQiOiI1N2JjZjNhNy01YmYwLTQ1M2QtODQ0Mi03ODhlMTA4OWI3MDIiLCJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIyNjE2NiIsImlzcyI6Imh0dHBzOi8vcmVsb2FkbHktc2FuZGJveC5hdXRoMC5jb20vIiwiaHR0cHM6Ly9yZWxvYWRseS5jb20vc2FuZGJveCI6dHJ1ZSwiaHR0cHM6Ly9yZWxvYWRseS5jb20vcHJlcGFpZFVzZXJJZCI6IjI2MTY2IiwiZ3R5IjoiY2xpZW50LWNyZWRlbnRpYWxzIiwiYXVkIjoiaHR0cHM6Ly90b3B1cHMtaHMyNTYtc2FuZGJveC5yZWxvYWRseS5jb20iLCJuYmYiOjE3NDMyNzIzNTQsImF6cCI6IjI2MTY2Iiwic2NvcGUiOiJzZW5kLXRvcHVwcyByZWFkLW9wZXJhdG9ycyByZWFkLXByb21vdGlvbnMgcmVhZC10b3B1cHMtaGlzdG9yeSByZWFkLXByZXBhaWQtYmFsYW5jZSByZWFkLXByZXBhaWQtY29tbWlzc2lvbnMiLCJleHAiOjE3NDMzNTg3NTQsImh0dHBzOi8vcmVsb2FkbHkuY29tL2p0aSI6Ijc0NjE0MjAzLWE0N2UtNGFjZi1hNmMzLWNkYmMwMzk3NDA1NSIsImlhdCI6MTc0MzI3MjM1NCwianRpIjoiOGIyOWY3MzItNDA1Zi00OWIwLWI2YjAtNTI5MjgxZGYzNzdlIn0.DO7CZ50EXbKwvWsiYaJX1mwvP-TYacVYhRuTfdSVAHw';

    const service = new ReloadlyService(ACCESS_TOKEN);
    
    // Example: Process top-up for MTN Nigeria number
    await service.processTopUp(
        '2347049670618',  // Phone number (without +)
        'NG',             // Country code
        1               // Amount in USD
    );
})().catch(console.error);