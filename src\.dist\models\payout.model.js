"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SUPPORTED_CURRENCIES = exports.PAYOUT_STATUSES = exports.PayoutAccount = exports.Payout = void 0;
const mongoose_1 = __importStar(require("mongoose"));
// Supported currencies configuration
const SUPPORTED_CURRENCIES = ['USD', 'LRD', 'SLL', 'GNF'];
exports.SUPPORTED_CURRENCIES = SUPPORTED_CURRENCIES;
// Payout Account Schema
const PayoutAccountSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    accountType: {
        type: String,
        required: true,
        enum: ['bank', 'savings', 'current', 'mobile_money']
    },
    accountName: {
        type: String,
        required: true,
        trim: true
    },
    accountNumber: {
        type: String,
        required: true,
        trim: true,
        index: true
    },
    bankName: {
        type: String,
        trim: true
    },
    bankCode: {
        type: String,
        trim: true
    },
    currency: {
        type: String,
        required: true,
        enum: SUPPORTED_CURRENCIES,
        index: true
    },
    country: {
        type: String,
        required: true,
        trim: true
    },
    isDefault: {
        type: Boolean,
        default: false,
        index: true
    },
    isVerified: {
        type: Boolean,
        default: false
    },
    verificationDetails: {
        verifiedAt: Date,
        verifiedBy: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'User'
        },
        method: String
    },
    metadata: mongoose_1.Schema.Types.Mixed,
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});
// Add compound index to ensure one payout account per currency per user
PayoutAccountSchema.index({ userId: 1, currency: 1 }, { unique: true });
// Payout Status Types
const PAYOUT_STATUSES = [
    'pending', // Initial state
    'approved', // Approved by admin
    'processing', // Sent to payment processor
    'completed', // Successfully processed
    'failed', // Failed at any stage
    'cancelled' // Cancelled by user or admin
];
exports.PAYOUT_STATUSES = PAYOUT_STATUSES;
// Payout Schema
const PayoutSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    payoutAccountId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'PayoutAccount',
        required: true,
        index: true
    },
    reference: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    amount: {
        type: Number,
        required: true,
        min: 0.01
    },
    fee: {
        type: Number,
        default: 0
    },
    netAmount: {
        type: Number,
        required: true
    },
    currency: {
        type: String,
        required: true,
        enum: SUPPORTED_CURRENCIES,
        index: true
    },
    note: {
        type: String,
        trim: true
    },
    status: {
        type: String,
        required: true,
        enum: PAYOUT_STATUSES,
        default: 'pending',
        index: true
    },
    statusHistory: [
        {
            status: {
                type: String,
                enum: PAYOUT_STATUSES,
                required: true
            },
            changedAt: {
                type: Date,
                default: Date.now
            },
            changedBy: {
                type: mongoose_1.Schema.Types.ObjectId,
                ref: 'User'
            },
            reason: String
        }
    ],
    destination: {
        accountType: String,
        accountName: String,
        accountNumber: String,
        bankName: String,
        bankCode: String,
        country: String
    },
    processorResponse: mongoose_1.Schema.Types.Mixed,
    metadata: mongoose_1.Schema.Types.Mixed,
    initiatedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    approvedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User'
    },
    completedAt: Date,
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});
// Auto-generate reference before save
PayoutSchema.pre('save', function (next) {
    if (!this.reference) {
        // Generate a unique reference: PAY-{timestamp}-{random}
        this.reference = `PAY-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    }
    // Calculate net amount
    this.netAmount = this.amount - this.fee;
    next();
});
// Create and export the models
const PayoutAccount = mongoose_1.default.model('PayoutAccount', PayoutAccountSchema);
exports.PayoutAccount = PayoutAccount;
const Payout = mongoose_1.default.model('Payout', PayoutSchema);
exports.Payout = Payout;
