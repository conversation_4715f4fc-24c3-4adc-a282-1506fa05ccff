import mongoose, { Schema, Document, ObjectId } from 'mongoose';

const BeneficiarySchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },

  name: {
    type: String,
    required: true,
  },
  bankName: {
    type: String,
    required: true,
  },
  accountNumber: {
    type: String,
    required: true,
  },
  currency: {
    type: String,
  },
  country: {
    type: String,
  },
  phoneNumber: {
    type: String,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});


export interface IBeneficiary extends Document {
  userId: ObjectId; 
  name: string;
  bankName: string;
  accountNumber: string;
  currency: string;
  country: string;
  phoneNumber?: string;
  createdAt: Date;
  updatedAt: Date;
}

const Beneficiary = mongoose.model<IBeneficiary>('Beneficiary', BeneficiarySchema);

export default Beneficiary;