"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const AddressSchema = new mongoose_1.Schema({
    line1: { type: String },
    line2: { type: String },
    city: { type: String },
    state: { type: String, required: true },
    postal_code: { type: String, required: true },
    country: { type: String, required: true },
});
const KycDocumentSchema = new mongoose_1.Schema({
    type: {
        type: String,
        required: true,
        enum: [
            'government_id',
            'passport',
            'drivers_license',
            'national_id',
            'business_registration',
            'proof_of_business',
            'tax_document'
        ]
    },
    category: {
        type: String,
        required: true,
        enum: ['personal', 'business']
    },
    url: { type: String, required: true },
    verified: { type: Boolean, default: false },
    uploadedAt: { type: Date, default: Date.now },
    verifiedAt: { type: Date },
    status: {
        type: String,
        enum: ['pending', 'approved', 'rejected', 'not_submitted'],
        default: 'not_submitted'
    },
    notes: { type: String },
});
const UserSchema = new mongoose_1.Schema({
    // Common fields for both Client and Business
    accountType: { type: String, enum: ['client', 'business'], required: true },
    full_legal_name: { type: String },
    first_name: { type: String },
    last_name: { type: String },
    date_of_birth: { type: Date },
    home_address: { type: AddressSchema },
    phone_number: { type: String },
    social_security: { type: String },
    country_of_residence: { type: String },
    last_login: { type: Date, default: null },
    payoutAccounts: [{
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Account.payoutAccounts'
        }],
    // Fields specific to Business
    legal_business_name: { type: String }, // Only for Business
    business_address: { type: AddressSchema }, // Use the AddressSchema for business address
    type_of_business_entity: { type: String }, // Only for Business
    tax_identification_number: { type: String }, // Only for Business
    country_of_business_residence: { type: String }, // Only for Business
    bank_account_details: { type: String }, // Only for Business
    estimated_transaction_volume: { type: String }, // Only for Business
    // KYC Documents
    documents: [KycDocumentSchema],
    // Authentication and verification fields
    email: { type: String },
    verificationCode: { type: String },
    verificationCodeExpiry: { type: Date },
    password: { type: String },
    kyc_status: { type: String, default: 'pending' },
    userIsVerified: { type: Boolean, default: false },
    role: { type: String, default: 'user' },
    permissions: { type: [String], default: [] },
    client_id: { type: String },
    client_secret: { type: String },
    redirect_uri: { type: String },
    loginPublicKey: { type: String },
    nonceExpiry: { type: Date, default: null },
    stripeAccountId: { type: String, default: null }, // Stripe account ID
    externalStripeAccountId: { type: String, default: null }, // External account ID
    profilePicture: {
        url: { type: String, default: null },
        updatedAt: { type: Date }
    },
    kycDocuments: [{
            type: { type: String, required: true }, // e.g., 'passport', 'drivers_license', 'national_id'
            url: { type: String, required: true },
            verified: { type: Boolean, default: false },
            uploadedAt: { type: Date, default: Date.now },
            verifiedAt: { type: Date },
            status: { type: String, enum: ['pending', 'approved', 'rejected'], default: 'pending' }
        }],
    kyc: {
        personal: {
            government_id: [KycDocumentSchema],
            passport: [KycDocumentSchema],
            drivers_license: [KycDocumentSchema],
            national_id: [KycDocumentSchema],
            status: {
                type: String,
                enum: ['not_submitted', 'pending', 'verified'],
                default: 'not_submitted'
            }
        },
        business: {
            business_registration: [KycDocumentSchema],
            proof_of_business: [KycDocumentSchema],
            tax_document: [KycDocumentSchema],
            status: {
                type: String,
                enum: ['not_submitted', 'pending', 'verified'],
                default: 'not_submitted'
            }
        }
    }
});
// User Model
const User = mongoose_1.default.model('User', UserSchema);
exports.default = User;
