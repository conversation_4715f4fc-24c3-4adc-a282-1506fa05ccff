"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const payout_controller_1 = require("../controllers/payout.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const payoutRouter = express_1.default.Router();
payoutRouter.post('/payout', auth_middleware_1.authenticateToken, payout_controller_1.requestPayout);
payoutRouter.post('/payout/account/add', auth_middleware_1.authenticateToken, payout_controller_1.addPayoutAccount);
payoutRouter.put('/payout/account/update', auth_middleware_1.authenticateToken, payout_controller_1.updatePayoutAccount);
payoutRouter.get('/payout/:userId/fetch-acc', auth_middleware_1.authenticateToken, payout_controller_1.getPayoutAccounts);
payoutRouter.get('/payout/:userId', auth_middleware_1.authenticateToken, payout_controller_1.getUserPayouts);
exports.default = payoutRouter;
