import express from 'express';
import { requestPayout, addPayoutAccount, updatePayoutAccount, getPayoutAccounts, getUserPayouts } from '../controllers/payout.controller';
import { authenticateToken } from '../middleware/auth.middleware';

const payoutRouter = express.Router();

payoutRouter.post('/payout', authenticateToken,  requestPayout);
payoutRouter.post('/payout/account/add', authenticateToken, addPayoutAccount);
payoutRouter.put('/payout/account/update', authenticateToken, updatePayoutAccount);
payoutRouter.get('/payout/:userId/fetch-acc', authenticateToken, getPayoutAccounts);
payoutRouter.get('/payout/:userId', authenticateToken, getUserPayouts);



export default payoutRouter;