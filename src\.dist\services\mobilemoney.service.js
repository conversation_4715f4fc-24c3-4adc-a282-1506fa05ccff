"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.mmapiService = void 0;
// @ts-ignore
const mmapi = __importStar(require("mmapi-nodejs-sdk"));
class MMAPIService {
    constructor() {
        this.consumerKey = process.env.CONSUMER_KEY || '';
        this.consumerSecret = process.env.CONSUMER_SECRET || '';
        this.apiKey = process.env.API_KEY || '';
        this.securityOption = process.env.SECURITY_OPTION || 'DEVELOPMENT_LEVEL';
        this.callbackUrl = process.env.CALLBACK_URL || '';
        this.environment = this.setupEnvironment();
        this.client = this.setupClient();
    }
    /**
     * Set up the MMAPI environment (Sandbox or Live).
     * @returns {mmapi.core.Environment} - MMAPI environment instance.
     */
    setupEnvironment() {
        if (process.env.NODE_ENV === 'production') {
            return new mmapi.core.LiveEnvironment(this.consumerKey, this.consumerSecret, this.apiKey, this.securityOption, this.callbackUrl);
        }
        return new mmapi.core.SandboxEnvironment(this.consumerKey, this.consumerSecret, this.apiKey, this.securityOption, this.callbackUrl);
    }
    /**
     * Set up the MMAPI HTTP client.
     * @returns {mmapi.core.MobileMoneyApiHttpClient} - MMAPI HTTP client instance.
     */
    setupClient() {
        return new mmapi.core.MobileMoneyApiHttpClient(this.environment);
    }
    /**
     * Get the MMAPI HTTP client instance.
     * @returns {mmapi.core.MobileMoneyApiHttpClient} - MMAPI HTTP client instance.
     */
    getClient() {
        return this.client;
    }
    /**
     * Example method to create a merchant transaction.
     * @param {Object} payload - Transaction payload.
     * @returns {Promise<any>} - Result of the transaction.
     */
    async createMerchantTransaction(payload) {
        try {
            const result = await this.client.createMerchantTransaction(payload);
            return result;
        }
        catch (error) {
            console.error('Error creating merchant transaction:', error);
            throw error;
        }
    }
    /**
     * Example method to retrieve account details.
     * @param {string} accountId - Account identifier.
     * @returns {Promise<any>} - Account details.
     */
    async getAccountDetails(accountId) {
        try {
            const result = await this.client.viewAccount({ identifierType1: accountId });
            return result;
        }
        catch (error) {
            console.error('Error fetching account details:', error);
            throw error;
        }
    }
}
// Export the service as a singleton instance
exports.mmapiService = new MMAPIService();
