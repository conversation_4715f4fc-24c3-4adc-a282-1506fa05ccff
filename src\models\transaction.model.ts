import mongoose, { Schema, Document } from 'mongoose';

export interface ITransaction extends Document {
  transactionId: string;
  type: string;
  title: string;
  amount: number;
  sender: {
    userId: mongoose.Types.ObjectId;
    name: string;
    accountNumber: string;
    username: string;
  };
  recipient?: {
    userId: mongoose.Types.ObjectId;
    name: string;
    accountNumber: string;
    username: string;
    email: string;
  };
  customDescription?: string;
  predefinedDescription: string;
  status: string;
  timestamp: Date;
  cardTransaction?: {
    cardId: string;
    cardType: string;
  };
  receipt?: string;
}

const TransactionSchema = new Schema<ITransaction>({
  transactionId: { type: String, required: true },
  type: {
    type: String,
    required: true,
    enum: ['DEBIT', 'CREDIT', 'TRANSFER', 'SWAP', 'WITHDRAW', 'DEPOSIT', 'CARD'],
  },
  title: { type: String, required: true },
  amount: { type: Number, required: true },
  sender: {
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    name: { type: String, required: true },
    accountNumber: { type: String, required: true },
    username: { type: String },
  },
  recipient: {
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    name: { type: String },
    accountNumber: { type: String },
    username: { type: String },
    email: { type: String },
  },
  customDescription: { type: String },
  predefinedDescription: { type: String, required: true },
  status: {
    type: String,
    required: true,
    enum: ['PENDING', 'SUCCESS', 'FAILED'],
    default: 'PENDING',
  },
  timestamp: { type: Date, default: Date.now },
  cardTransaction: {
    cardId: { type: String },
    cardType: { type: String },
  },
  receipt: { type: String }, 
});

// Custom validation to make recipient optional for CARD transactions
TransactionSchema.pre('validate', function (next) {
  if (this.type !== 'CARD' && !this.recipient) {
    this.invalidate('recipient', 'Recipient is required');
  }
  next();
});

export const Transaction = mongoose.model<ITransaction>('Transaction', TransactionSchema);