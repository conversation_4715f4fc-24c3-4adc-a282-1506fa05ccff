// @ts-ignore
import * as mmapi from 'mmapi-nodejs-sdk';

class MMAPIService {
  private consumerKey: string;
  private consumerSecret: string;
  private apiKey: string;
  private securityOption: string; // DEVELOPMENT_LEVEL, STANDARD_LEVEL, ENHANCED_LEVEL
  private callbackUrl: string;

  private environment: mmapi.core.Environment;
  private client: mmapi.core.MobileMoneyApiHttpClient;

  constructor() {
    this.consumerKey = process.env.CONSUMER_KEY || '';
    this.consumerSecret = process.env.CONSUMER_SECRET || '';
    this.apiKey = process.env.API_KEY || '';
    this.securityOption = process.env.SECURITY_OPTION || 'DEVELOPMENT_LEVEL';
    this.callbackUrl = process.env.CALLBACK_URL || '';

    this.environment = this.setupEnvironment();
    this.client = this.setupClient();
  }

  /**
   * Set up the MMAPI environment (Sandbox or Live).
   * @returns {mmapi.core.Environment} - MMAPI environment instance.
   */
  private setupEnvironment(): mmapi.core.Environment {
    if (process.env.NODE_ENV === 'production') {
      return new mmapi.core.LiveEnvironment(
        this.consumerKey,
        this.consumerSecret,
        this.apiKey,
        this.securityOption,
        this.callbackUrl
      );
    }
    return new mmapi.core.SandboxEnvironment(
      this.consumerKey,
      this.consumerSecret,
      this.apiKey,
      this.securityOption,
      this.callbackUrl
    );
  }

  /**
   * Set up the MMAPI HTTP client.
   * @returns {mmapi.core.MobileMoneyApiHttpClient} - MMAPI HTTP client instance.
   */
  private setupClient(): mmapi.core.MobileMoneyApiHttpClient {
    return new mmapi.core.MobileMoneyApiHttpClient(this.environment);
  }

  /**
   * Get the MMAPI HTTP client instance.
   * @returns {mmapi.core.MobileMoneyApiHttpClient} - MMAPI HTTP client instance.
   */
  public getClient(): mmapi.core.MobileMoneyApiHttpClient {
    return this.client;
  }

  /**
   * Example method to create a merchant transaction.
   * @param {Object} payload - Transaction payload.
   * @returns {Promise<any>} - Result of the transaction.
   */
  public async createMerchantTransaction(payload: any): Promise<any> {
    try {
      const result = await this.client.createMerchantTransaction(payload);
      return result;
    } catch (error) {
      console.error('Error creating merchant transaction:', error);
      throw error;
    }
  }

  /**
   * Example method to retrieve account details.
   * @param {string} accountId - Account identifier.
   * @returns {Promise<any>} - Account details.
   */
  public async getAccountDetails(accountId: string): Promise<any> {
    try {
      const result = await this.client.viewAccount({ identifierType1: accountId });
      return result;
    } catch (error) {
      console.error('Error fetching account details:', error);
      throw error;
    }
  }
}

// Export the service as a singleton instance
export const mmapiService = new MMAPIService();