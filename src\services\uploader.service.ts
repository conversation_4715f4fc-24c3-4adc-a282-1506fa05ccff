import { v2 as cloudinary } from 'cloudinary';
import { Request } from 'express';
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import { extractPublicId } from 'cloudinary-build-url';
import dotenv from 'dotenv';

dotenv.config();

// Cloudinary configuration
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Use the Lambda-compatible temporary directory
const tmpDir = process.env.NODE_ENV === 'production' ? '/tmp' : path.resolve(__dirname, '../uploads/temp');

// Ensure tmp directory exists
if (!fs.existsSync(tmpDir)) {
  fs.mkdirSync(tmpDir, { recursive: true });
}

// Multer storage configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, tmpDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  },
});

// Configure multer with file filters
export const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    const allowedMimes = [
      'image/jpeg',
      'image/png',
      'image/jpg',
      'application/pdf', // Allow PDFs for KYC documents
    ];

    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG and PDF files are allowed.'));
    }
  }
});

// Upload to Cloudinary function
export const uploadToCloudinary = async (filePath: string, folder: string): Promise<string> => {
  try {
    const result = await cloudinary.uploader.upload(filePath, { 
      folder,
      resource_type: 'auto',
      allowed_formats: ['jpg', 'png', 'jpeg', 'pdf'], // Add PDF for KYC documents
    });
    
    // Clean up local file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    
    return result.secure_url;
  } catch (error) {
    console.error('Cloudinary upload failed:', error);
    // Clean up local file on error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw new Error('Document upload failed');
  }
};

// Define upload types
export type UploadType = 'profilePicture' | 'kyc' | 'idCard' | 'selfie' | 'businessDocs';

// Unified uploader function
export const unifiedUploader = async (req: Request, uploadType: UploadType): Promise<string[]> => {
  try {
    // Handle different types of file inputs
    const files = (() => {
      if (req.files) {
        if (Array.isArray(req.files)) return req.files;
        return Object.values(req.files).flat();
      }
      if (req.file) return [req.file];
      return [];
    })();

    if (files.length === 0) {
      console.warn('No files found in request');
      return [];
    }

    // Map upload types to Cloudinary folders
    const folderMap: Record<UploadType, string> = {
      profilePicture: 'sangapay/profile_pictures',
      kyc: 'sangapay/kyc_documents',
      idCard: 'sangapay/id_cards',
      selfie: 'sangapay/selfies',
      businessDocs: 'sangapay/business_documents',
    };

    const folder = folderMap[uploadType];

    // Upload files and handle errors for each file individually
    const uploadPromises = files.map(async (file) => {
      try {
        if (!file.path) {
          console.error('File path is missing:', file);
          return null;
        }

        console.log(`Processing upload: ${file.originalname} to ${folder}`);
        return await uploadToCloudinary(file.path, folder);
      } catch (error) {
        console.error(`Failed to upload ${file.originalname}:`, error);
        return null;
      }
    });

    const results = await Promise.all(uploadPromises);
    return results.filter((url): url is string => url !== null);

  } catch (error) {
    console.error('Unhandled error in unifiedUploader:', error);
    throw error;
  }
};
