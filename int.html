<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Link Card</title>
  <script src="https://js.stripe.com/v3/"></script>
</head>
<body>
  <h1>Link Your Card</h1>
  <form id="card-form">
    <div id="card-element">
      <!-- Stripe Card Element will be inserted here -->
    </div>
    <button id="submit-button">Link Card</button>
  </form>

  <script>
    const stripe = Stripe('pk_test_51QkdIsRoh11lQUTEuw7x6SCTSoEQHHQiRvCRezwuymzrSggQx3shqt7qB0npLhUxMihBQs9Nj7tmgHchHs86m1gt00J4jvz4FX'); // Replace with your publishable key

    const elements = stripe.elements();
    const cardElement = elements.create('card');
    cardElement.mount('#card-element');

    const form = document.getElementById('card-form');
    form.addEventListener('submit', async (event) => {
      event.preventDefault();

      // Create a card token
      const { token, error } = await stripe.createToken(cardElement);

      if (error) {
        console.error(error);
        alert('Error creating card token. Please try again.');
        return;
      }

      // Send the token to your backend
      fetch('http://localhost:5000/api/v1/acc/link-card-to-stripe-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer 703966_7b22757365724964223a3738393939312c22636c69656e745f6964223a22436c5f35653734636630316361353266643462222c225f657870697279223a332e31353537363030303031373339373335652b32322c227065726d697373696f6e73223a5b2272656164222c227772697465222c22757064617465222c2264656c657465225d2c22746f6b656e54797065223a22616363657373227d.37a9a7e3f0280c7c_60126b1e0e474e4d`,

        },
        body: JSON.stringify({ token: token.id, userId: '6793da4691ccb86c93624402' }), // Replace with actual userId
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.status) {
            alert('Card linked successfully!');
          } else {
            alert('Error linking card: ' + data.message);
          }
        })
        .catch((error) => {
          console.error(error);
          alert('An error occurred. Please try again.');
        });
    });
  </script>
</body>
</html>