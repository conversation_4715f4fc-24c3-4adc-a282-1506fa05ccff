"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.unifiedUploader = exports.uploadToCloudinary = exports.upload = void 0;
const cloudinary_1 = require("cloudinary");
const multer_1 = __importDefault(require("multer"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
// Cloudinary configuration
cloudinary_1.v2.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
});
// Use the Lambda-compatible temporary directory
const tmpDir = process.env.NODE_ENV === 'production' ? '/tmp' : path_1.default.resolve(__dirname, '../uploads/temp');
// Ensure tmp directory exists
if (!fs_1.default.existsSync(tmpDir)) {
    fs_1.default.mkdirSync(tmpDir, { recursive: true });
}
// Multer storage configuration
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, tmpDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path_1.default.extname(file.originalname));
    },
});
// Configure multer with file filters
exports.upload = (0, multer_1.default)({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
    fileFilter: (req, file, cb) => {
        const allowedMimes = [
            'image/jpeg',
            'image/png',
            'image/jpg',
            'application/pdf', // Allow PDFs for KYC documents
        ];
        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        }
        else {
            cb(new Error('Invalid file type. Only JPEG, PNG and PDF files are allowed.'));
        }
    }
});
// Upload to Cloudinary function
const uploadToCloudinary = async (filePath, folder) => {
    try {
        const result = await cloudinary_1.v2.uploader.upload(filePath, {
            folder,
            resource_type: 'auto',
            allowed_formats: ['jpg', 'png', 'jpeg', 'pdf'], // Add PDF for KYC documents
        });
        // Clean up local file
        if (fs_1.default.existsSync(filePath)) {
            fs_1.default.unlinkSync(filePath);
        }
        return result.secure_url;
    }
    catch (error) {
        console.error('Cloudinary upload failed:', error);
        // Clean up local file on error
        if (fs_1.default.existsSync(filePath)) {
            fs_1.default.unlinkSync(filePath);
        }
        throw new Error('Document upload failed');
    }
};
exports.uploadToCloudinary = uploadToCloudinary;
// Unified uploader function
const unifiedUploader = async (req, uploadType) => {
    try {
        // Handle different types of file inputs
        const files = (() => {
            if (req.files) {
                if (Array.isArray(req.files))
                    return req.files;
                return Object.values(req.files).flat();
            }
            if (req.file)
                return [req.file];
            return [];
        })();
        if (files.length === 0) {
            console.warn('No files found in request');
            return [];
        }
        // Map upload types to Cloudinary folders
        const folderMap = {
            profilePicture: 'sangapay/profile_pictures',
            kyc: 'sangapay/kyc_documents',
            idCard: 'sangapay/id_cards',
            selfie: 'sangapay/selfies',
            businessDocs: 'sangapay/business_documents',
        };
        const folder = folderMap[uploadType];
        // Upload files and handle errors for each file individually
        const uploadPromises = files.map(async (file) => {
            try {
                if (!file.path) {
                    console.error('File path is missing:', file);
                    return null;
                }
                console.log(`Processing upload: ${file.originalname} to ${folder}`);
                return await (0, exports.uploadToCloudinary)(file.path, folder);
            }
            catch (error) {
                console.error(`Failed to upload ${file.originalname}:`, error);
                return null;
            }
        });
        const results = await Promise.all(uploadPromises);
        return results.filter((url) => url !== null);
    }
    catch (error) {
        console.error('Unhandled error in unifiedUploader:', error);
        throw error;
    }
};
exports.unifiedUploader = unifiedUploader;
