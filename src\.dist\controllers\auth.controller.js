"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetPassword = exports.verifyCode = exports.forgotPassword = exports.verifyUser = exports.verifyLoginCode = exports.requestLoginCode = exports.loginUser = exports.resendVerificationCode = exports.registerUser = exports.storePublicKey = exports.verifyNonceAndLogin = exports.generateAndStoreKeyPair = exports.generateNonceAPI = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const twilio_1 = __importDefault(require("twilio"));
const dotenv_1 = __importDefault(require("dotenv"));
const token_service_1 = __importDefault(require("../apisystem/service/token.service"));
const validator_1 = __importDefault(require("validator"));
const user_model_1 = __importDefault(require("../models/user.model"));
const crypto_1 = require("crypto");
const auth_service_1 = require("../services/auth.service");
const crypto_2 = __importDefault(require("crypto"));
dotenv_1.default.config();
const twilioClient = (0, twilio_1.default)(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
// Helper function to sanitize and validate input
const sanitizeInput = (input) => {
    return validator_1.default.escape(validator_1.default.trim(input));
};
// Function to generate a nonce
const generateNonce = () => {
    return crypto_2.default.randomBytes(16).toString('hex'); // Generate a long nonce
};
// API endpoint to generate nonce
const generateNonceAPI = async (req, res) => {
    const { phone_number } = req.body; // Accept phone_number
    // Input validation
    if (!phone_number) {
        return res.status(400).json({
            status: false,
            message: "MISSING_REQUIRED_FIELDS",
            meta: {
                error: "Phone number is required.",
                suggestions: ["Provide the phone number and try again."],
            },
        });
    }
    // Find user in database by phone number
    const user = await user_model_1.default.findOne({ phone_number });
    if (!user) {
        return res.status(404).json({
            status: false,
            message: "USER_NOT_FOUND",
            meta: {
                error: "User not found.",
                suggestions: ["Check the phone number and try again."],
            },
        });
    }
    // Generate a nonce and save it to the user's data with an expiry time
    const nonce = generateNonce();
    const nonceExpiry = new Date(Date.now() + 2 * 60 * 1000); // 2 minutes from now
    user.loginPublicKey = nonce;
    user.nonceExpiry = nonceExpiry; // Save the expiry time
    await user.save();
    // Return the nonce to the user
    return res.status(200).json({
        status: true,
        message: "NONCE_GENERATED",
        data: {
            nonce,
            nonceExpiry,
        },
    });
};
exports.generateNonceAPI = generateNonceAPI;
/**
 * Generate ECDSA Key Pair and Store Public Key
 */
const generateAndStoreKeyPair = async (req, res) => {
    const { phone_number } = req.body;
    if (!phone_number) {
        return res.status(400).json({
            status: false,
            message: "MISSING_REQUIRED_FIELDS",
            meta: { error: "Phone number is required." },
        });
    }
    try {
        // Generate ECDSA key pair
        const keyPair = await crypto_1.subtle.generateKey({ name: "ECDSA", namedCurve: "P-256" }, true, ["sign", "verify"]);
        // Export keys
        const publicKey = Buffer.from(await crypto_1.subtle.exportKey("spki", keyPair.publicKey)).toString("base64");
        const privateKey = Buffer.from(await crypto_1.subtle.exportKey("pkcs8", keyPair.privateKey)).toString("base64");
        // Store public key in database
        let user = await user_model_1.default.findOne({ phone_number });
        if (!user) {
            user = new user_model_1.default({ phone_number, loginPublicKey: publicKey });
        }
        else {
            user.loginPublicKey = publicKey;
        }
        await user.save();
        // Sign phone number as proof of key ownership
        const signature = await (0, auth_service_1.signData)(keyPair.privateKey, phone_number);
        return res.status(201).json({
            status: true,
            message: "KEY_PAIR_GENERATED",
            data: { privateKey, signature } // Send private key & signature to user
        });
    }
    catch (error) {
        return res.status(500).json({
            status: false,
            message: "KEY_GENERATION_FAILED",
            meta: { error: error.message },
        });
    }
};
exports.generateAndStoreKeyPair = generateAndStoreKeyPair;
// Function to verify signature (if needed)
const verifyNonceAndLogin = async (req, res) => {
    const { phone_number, nonce } = req.body; // Accept phone_number and nonce
    // Input validation
    if (!phone_number || !nonce) {
        return res.status(400).json({
            status: false,
            message: "MISSING_REQUIRED_FIELDS",
            meta: {
                error: "Phone number and nonce are required.",
                suggestions: ["Provide both fields and try again."],
            },
        });
    }
    try {
        // Find user in database by phone number
        const user = await user_model_1.default.findOne({ phone_number });
        // Check if user exists and the nonce matches
        if (!user || user.loginPublicKey !== nonce) {
            return res.status(401).json({
                status: false,
                message: "INVALID_NONCE",
                meta: {
                    error: "The provided nonce is invalid or has already been used.",
                    suggestions: ["Ensure the correct nonce is provided."],
                },
            });
        }
        // Check if the nonce has expired
        if (user.nonceExpiry && new Date() > user.nonceExpiry) {
            return res.status(401).json({
                status: false,
                message: "NONCE_EXPIRED",
                meta: {
                    error: "The provided nonce has expired.",
                    suggestions: ["Generate a new nonce and try again."],
                },
            });
        }
        // Generate authentication tokens
        const tokenService = new token_service_1.default();
        const grantToken = tokenService.generateGrantToken({ userId: user._id }, 600, ["read", "write", "update", "delete"], tokenService.INTERNAL_CALL_KEY, "grant");
        const { access_token, refresh_token } = tokenService.generateAccessToken("authorization_code", user.client_id, user.client_secret, grantToken);
        // Update last login timestamp and clear the nonce and expiry
        user.last_login = new Date();
        user.loginPublicKey = ''; // Clear the nonce after use
        user.nonceExpiry = null; // Clear the nonce expiry
        await user.save();
        // Return success response
        return res.status(200).json({
            status: true,
            message: "LOGIN_SUCCESSFUL",
            data: {
                id: user._id,
                phone_number: user.phone_number,
                access_token,
                refresh_token,
                expires_in: 3600,
                last_login: user.last_login,
            },
        });
    }
    catch (error) {
        console.error("Error verifying nonce and logging in:", error);
        return res.status(500).json({
            status: false,
            message: "NONCE_VERIFICATION_FAILED",
            meta: {
                error: error.message,
                suggestions: ["Ensure the request is correctly formatted.", "Try again later."],
            },
        });
    }
};
exports.verifyNonceAndLogin = verifyNonceAndLogin;
const storePublicKey = async (req, res) => {
    const { phone_number, publicKey } = req.body;
    // Input validation and sanitization
    if (!phone_number || !publicKey) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'Phone number and public key are required.',
                suggestions: ['Provide both phone number and public key.'],
            },
        });
    }
    try {
        // Find the user by phone number
        const user = await user_model_1.default.findOne({ phone_number });
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided phone number does not exist.',
                    suggestions: ['Check the phone number and try again.'],
                },
            });
        }
        // Update the user's loginPublicKey field
        user.loginPublicKey = publicKey;
        await user.save();
        // Return success response
        return res.status(200).json({
            status: true,
            message: 'PUBLIC_KEY_STORED_SUCCESSFULLY',
            data: {
                id: user._id,
                phone_number: user.phone_number,
                loginPublicKey: user.loginPublicKey,
            },
        });
    }
    catch (error) {
        console.error('Error during public key storage:', error);
        // Return error response
        return res.status(500).json({
            status: false,
            message: 'PUBLIC_KEY_STORAGE_FAILED',
            meta: {
                error: error.message,
                suggestions: [
                    'Check your input data for errors.',
                    'Ensure the database is running.',
                    'Try again later.',
                ],
            },
        });
    }
};
exports.storePublicKey = storePublicKey;
// User Registration Function
const registerUser = async (req, res) => {
    const { phone_number, password } = req.body;
    const accountType = "client";
    // Validate phone number presence
    if (!phone_number) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'Phone number is required.',
                suggestions: ['Provide a valid phone number.'],
            },
        });
    }
    const sanitizedPhoneNumber = sanitizeInput(phone_number);
    const sanitizedPassword = password ? sanitizeInput(password) : null;
    // Validate phone number format
    if (!validator_1.default.isMobilePhone(sanitizedPhoneNumber, 'any')) {
        return res.status(400).json({
            status: false,
            message: 'INVALID_PHONE_NUMBER',
            meta: {
                error: 'The provided phone number is invalid.',
                suggestions: ['Provide a valid phone number.'],
            },
        });
    }
    try {
        // Check if the phone number is already registered
        const existingUser = await user_model_1.default.findOne({ phone_number: sanitizedPhoneNumber });
        if (existingUser) {
            if (existingUser.userIsVerified) {
                return res.status(400).json({
                    status: false,
                    message: 'PHONE_NUMBER_ALREADY_REGISTERED',
                    meta: {
                        error: 'Phone number is already in use.',
                        suggestions: ['Use a different phone number.', 'Try logging in instead.'],
                    },
                });
            }
            else {
                return res.status(400).json({
                    status: false,
                    message: 'USER_NOT_VERIFIED',
                    meta: {
                        error: 'User is registered but not verified.',
                        suggestions: ['Verify your account using the verification code.', 'Request a new verification code if needed.'],
                    },
                });
            }
        }
        // Generate a verification code (e.g., 6-digit code)
        const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
        const verificationCodeExpiry = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes expiry
        // Hash the password if provided
        let hashedPassword = null;
        if (sanitizedPassword) {
            const saltRounds = 10;
            hashedPassword = await bcrypt_1.default.hash(sanitizedPassword, saltRounds);
        }
        // Save the user to the database (unverified)
        const newUser = new user_model_1.default({
            phone_number: sanitizedPhoneNumber,
            password: hashedPassword, // Can be null if not provided
            accountType,
            verificationCode,
            verificationCodeExpiry,
            userIsVerified: false,
        });
        await newUser.save();
        const expiryTime = "5 minutes";
        // Send the verification code via SMS (using Twilio)
        await twilioClient.messages.create({
            body: `Your verification code is: ${verificationCode}. It expires in ${expiryTime}.`,
            from: process.env.TWILIO_PHONE_NUMBER,
            to: sanitizedPhoneNumber,
        });
        return res.status(201).json({
            status: true,
            message: 'USER_REGISTERED_SUCCESSFULLY',
            data: {
                id: newUser._id,
                phone_number: newUser.phone_number,
                verificationCodeExpiry: newUser.verificationCodeExpiry,
            },
        });
    }
    catch (error) {
        console.error('Error during user registration:', error);
        return res.status(500).json({
            status: false,
            message: 'REGISTRATION_FAILED',
            meta: {
                error: error.message,
                suggestions: [
                    'Check your input data for errors.',
                    'Ensure the SMS service is running.',
                    'Try again later.',
                ],
            },
        });
    }
};
exports.registerUser = registerUser;
const resendVerificationCode = async (req, res) => {
    const { phone_number } = req.body;
    // Input validation
    if (!phone_number) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'Phone number is required.',
                suggestions: ['Provide a phone number.'],
            },
        });
    }
    const sanitizedPhoneNumber = sanitizeInput(phone_number);
    try {
        // Find the user by phone number
        const user = await user_model_1.default.findOne({ phone_number: sanitizedPhoneNumber });
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided phone number does not exist.',
                    suggestions: ['Check the phone number and try again.'],
                },
            });
        }
        // Check if the user is already verified
        if (user.userIsVerified) {
            return res.status(400).json({
                status: false,
                message: 'USER_ALREADY_VERIFIED',
                meta: {
                    error: 'User is already verified.',
                    suggestions: ['No need to resend the verification code.'],
                },
            });
        }
        // Check if the previous verification code has expired
        const now = new Date();
        if (user.verificationCodeExpiry && user.verificationCodeExpiry > now) {
            return res.status(400).json({
                status: false,
                message: 'VERIFICATION_CODE_STILL_VALID',
                meta: {
                    error: 'The previous verification code is still valid.',
                    suggestions: ['Use the existing verification code.', 'Wait for the code to expire before requesting a new one.'],
                },
            });
        }
        // Generate a new verification code
        const newVerificationCode = Math.floor(100000 + Math.random() * 900000).toString();
        const newVerificationCodeExpiry = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
        // Update the user with the new verification code and expiry
        user.verificationCode = newVerificationCode;
        user.verificationCodeExpiry = newVerificationCodeExpiry;
        await user.save();
        // Format the expiration time for the SMS message
        const expiryTime = newVerificationCodeExpiry.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
        });
        // Send the new verification code via SMS
        await twilioClient.messages.create({
            body: `Your new verification code is: ${newVerificationCode}. It will expire at ${expiryTime}.`,
            from: process.env.TWILIO_PHONE_NUMBER,
            to: sanitizedPhoneNumber,
        });
        // Return success response
        return res.status(200).json({
            status: true,
            message: 'VERIFICATION_CODE_RESENT',
            data: {
                id: user._id,
                phone_number: user.phone_number,
                verificationCodeExpiry: user.verificationCodeExpiry, // Include new expiry time
            },
        });
    }
    catch (error) {
        console.error('Error during resend verification code:', error);
        // Return error response
        return res.status(500).json({
            status: false,
            message: 'RESEND_VERIFICATION_CODE_FAILED',
            meta: {
                error: error.message,
                suggestions: [
                    'Check your input data for errors.',
                    'Ensure the SMS service is running.',
                    'Try again later.',
                ],
            },
        });
    }
};
exports.resendVerificationCode = resendVerificationCode;
const loginUser = async (req, res) => {
    const { phone_number, password } = req.body;
    // Input validation and sanitization
    if (!phone_number || !password) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'Phone number and password are required.',
                suggestions: ['Provide both phone number and password.'],
            },
        });
    }
    const sanitizedPhoneNumber = sanitizeInput(phone_number);
    const sanitizedPassword = sanitizeInput(password);
    // Validate phone number format
    if (!validator_1.default.isMobilePhone(sanitizedPhoneNumber, 'any')) {
        return res.status(400).json({
            status: false,
            message: 'INVALID_PHONE_NUMBER',
            meta: {
                error: 'The provided phone number is invalid.',
                suggestions: ['Provide a valid phone number.'],
            },
        });
    }
    try {
        // Find the user by phone number
        const user = await user_model_1.default.findOne({ phone_number: sanitizedPhoneNumber });
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided phone number does not exist.',
                    suggestions: ['Check the phone number and try again.'],
                },
            });
        }
        // Check if the user is verified
        if (!user.userIsVerified) {
            return res.status(400).json({
                status: false,
                message: 'USER_NOT_VERIFIED',
                meta: {
                    error: 'User is registered but not verified.',
                    suggestions: ['Verify your account using the verification code.', 'Request a new verification code if needed.'],
                },
            });
        }
        // Verify the password
        const isPasswordValid = await bcrypt_1.default.compare(sanitizedPassword, user.password);
        if (!isPasswordValid) {
            return res.status(400).json({
                status: false,
                message: 'INVALID_PASSWORD',
                meta: {
                    error: 'The provided password is incorrect.',
                    suggestions: ['Check the password and try again.'],
                },
            });
        }
        // Generate a grant token (used as a base for access and refresh tokens)
        const tokenService = new token_service_1.default();
        const grantToken = tokenService.generateGrantToken({ userId: user._id }, // Payload
        600, // Expiry time in seconds
        ['read', 'write', 'update', 'delete'], // Permissions
        tokenService.INTERNAL_CALL_KEY, // Internal call key
        'grant' // Token type
        );
        // Generate an access token and refresh token
        const { access_token, refresh_token } = tokenService.generateAccessToken('authorization_code', // Grant type
        user.client_id, // Client ID
        user.client_secret, // Client secret
        grantToken // Grant token
        );
        // Update the user's last login time
        user.last_login = new Date();
        await user.save();
        // Return success response with tokens and user details
        return res.status(200).json({
            status: true,
            message: 'LOGIN_SUCCESSFUL',
            data: {
                id: user._id,
                phone_number: user.phone_number,
                access_token,
                refresh_token,
                expires_in: 3600,
                last_login: user.last_login, // Include the updated last_login time in the response
            },
        });
    }
    catch (error) {
        console.error('Error during user login:', error);
        // Return error response
        return res.status(500).json({
            status: false,
            message: 'LOGIN_FAILED',
            meta: {
                error: error.message,
                suggestions: [
                    'Check your input data for errors.',
                    'Try again later.',
                ],
            },
        });
    }
};
exports.loginUser = loginUser;
const requestLoginCode = async (req, res) => {
    const { phone_number } = req.body;
    if (!phone_number) {
        return res.status(400).json({
            status: false,
            message: "MISSING_PHONE_NUMBER",
            meta: { error: "Phone number is required." },
        });
    }
    try {
        const user = await user_model_1.default.findOne({ phone_number });
        if (!user) {
            return res.status(404).json({
                status: false,
                message: "USER_NOT_FOUND",
                meta: { error: "User with this phone number does not exist." },
            });
        }
        let verificationCode;
        let expiryTime = new Date();
        if (phone_number === "+*************") {
            // Set fixed code for demo account
            verificationCode = "123456";
            expiryTime.setMinutes(expiryTime.getMinutes() + 5);
        }
        else {
            // Generate a random verification code
            verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
            expiryTime.setMinutes(expiryTime.getMinutes() + 5); // Code expires in 5 minutes
        }
        // Store verification code in the database
        user.verificationCode = verificationCode;
        user.verificationCodeExpiry = expiryTime;
        await user.save();
        // Send the verification code via SMS (except for demo account)
        if (phone_number !== "+*************") {
            await twilioClient.messages.create({
                body: `Your login verification code is: ${verificationCode}. It will expire in 5 minutes.`,
                from: process.env.TWILIO_PHONE_NUMBER,
                to: phone_number,
            });
        }
        return res.status(200).json({
            status: true,
            message: "VERIFICATION_CODE_SENT",
            meta: { expiry: "5 minutes", code: phone_number === "+*************" ? "123456" : "hidden" },
        });
    }
    catch (error) {
        console.error("Error sending login code:", error);
        return res.status(500).json({
            status: false,
            message: "LOGIN_CODE_SEND_FAILED",
            meta: { error: error.message },
        });
    }
};
exports.requestLoginCode = requestLoginCode;
const verifyLoginCode = async (req, res) => {
    const { phone_number, verificationCode } = req.body;
    if (!phone_number || !verificationCode) {
        return res.status(400).json({
            status: false,
            message: "MISSING_REQUIRED_FIELDS",
            meta: { error: "Phone number and verification code are required." },
        });
    }
    try {
        // Find the user
        const user = await user_model_1.default.findOne({ phone_number });
        if (!user) {
            return res.status(404).json({
                status: false,
                message: "USER_NOT_FOUND",
            });
        }
        // Check if the verification code matches and is not expired
        if (!user.verificationCode || user.verificationCode !== verificationCode) {
            return res.status(400).json({
                status: false,
                message: "INVALID_VERIFICATION_CODE",
            });
        }
        const now = new Date();
        if (user.verificationCodeExpiry && user.verificationCodeExpiry < now) {
            return res.status(400).json({
                status: false,
                message: "VERIFICATION_CODE_EXPIRED",
            });
        }
        // Clear the verification code after successful login
        user.verificationCode = null;
        user.verificationCodeExpiry = null;
        user.last_login = new Date();
        await user.save();
        // Generate a grant token
        const tokenService = new token_service_1.default();
        const grantToken = tokenService.generateGrantToken({ userId: user._id }, 600, ["read", "write", "update", "delete"], tokenService.INTERNAL_CALL_KEY, "grant");
        // Generate access and refresh tokens
        const { access_token, refresh_token } = tokenService.generateAccessToken("authorization_code", user.client_id, user.client_secret, grantToken);
        return res.status(200).json({
            status: true,
            message: "LOGIN_SUCCESSFUL",
            data: {
                id: user._id,
                phone_number: user.phone_number,
                access_token,
                refresh_token,
                expires_in: 3600,
                last_login: user.last_login,
            },
        });
    }
    catch (error) {
        console.error("Error verifying login code:", error);
        return res.status(500).json({
            status: false,
            message: "LOGIN_VERIFICATION_FAILED",
            meta: { error: error.message },
        });
    }
};
exports.verifyLoginCode = verifyLoginCode;
// Verify User Function
const verifyUser = async (req, res) => {
    const { phone_number, verificationCode } = req.body;
    // Validate required fields
    if (!phone_number || !verificationCode) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'Phone number and verification code are required.',
                suggestions: ['Provide both phone number and verification code.'],
            },
        });
    }
    const sanitizedPhoneNumber = sanitizeInput(phone_number);
    const sanitizedVerificationCode = sanitizeInput(verificationCode);
    try {
        // Find the user by phone number
        const user = await user_model_1.default.findOne({ phone_number: sanitizedPhoneNumber });
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided phone number does not exist.',
                    suggestions: ['Check the phone number and try again.'],
                },
            });
        }
        // Ensure consistent data types for comparison
        if (String(user.verificationCode) !== sanitizedVerificationCode) {
            return res.status(400).json({
                status: false,
                message: 'INVALID_VERIFICATION_CODE',
                meta: {
                    error: 'The verification code is incorrect.',
                    suggestions: ['Check the code and try again.'],
                },
            });
        }
        const tokenService = new token_service_1.default();
        const { clientId, clientSecret } = await tokenService.generateClientIdAndSecret();
        // Generate a grant token (used as a base for access and refresh tokens)
        const grantToken = tokenService.generateGrantToken({ userId: user._id }, // Payload
        600, // Expiry time in seconds
        ['read', 'write', 'update', 'delete'], // Permissions
        tokenService.INTERNAL_CALL_KEY, // Internal call key
        'grant' // Token type
        );
        // Generate an access token and refresh token
        const { access_token, refresh_token } = tokenService.generateAccessToken('authorization_code', // Grant type
        clientId, // Client ID
        clientSecret, // Client secret
        grantToken // Grant token
        );
        // Mark the user as verified and remove the verification code
        await user_model_1.default.updateOne({ _id: user._id }, {
            $set: {
                userIsVerified: true,
                client_id: clientId,
                client_secret: clientSecret,
            },
            $unset: { verificationCode: '' }, // Remove the verificationCode field
        });
        // Return success response with tokens and user details
        return res.status(200).json({
            status: true,
            message: 'USER_VERIFIED_SUCCESSFULLY',
            data: {
                id: user._id,
                phone_number: user.phone_number,
                clientId,
                clientSecret,
                access_token,
                refresh_token,
                expires_in: 3600,
            },
        });
    }
    catch (error) {
        console.error('Error during user verification:', error);
        // Return error response
        return res.status(500).json({
            status: false,
            message: 'VERIFICATION_FAILED',
            meta: {
                error: error.message,
                suggestions: [
                    'Check your input data for errors.',
                    'Try again later.',
                ],
            },
        });
    }
};
exports.verifyUser = verifyUser;
// Forgot Password Function
const forgotPassword = async (req, res) => {
    const { phone_number } = req.body;
    // Validate required fields
    if (!phone_number) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'Phone number is required.',
                suggestions: ['Provide the phone number associated with your account.'],
            },
        });
    }
    const sanitizedPhoneNumber = sanitizeInput(phone_number);
    try {
        // Find the user by phone number
        const user = await user_model_1.default.findOne({ phone_number: sanitizedPhoneNumber });
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided phone number does not exist.',
                    suggestions: ['Check the phone number and try again.'],
                },
            });
        }
        // Generate a new verification code
        const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
        // Save the verification code to the user's document
        user.verificationCode = verificationCode;
        await user.save();
        // Send the verification code via SMS (using Twilio)
        await twilioClient.messages.create({
            body: `Your password reset verification code is: ${verificationCode}`,
            from: process.env.TWILIO_PHONE_NUMBER,
            to: sanitizedPhoneNumber,
        });
        // Return success response
        return res.status(200).json({
            status: true,
            message: 'VERIFICATION_CODE_SENT',
            data: {
                phone_number: user.phone_number,
            },
        });
    }
    catch (error) {
        console.error('Error during forgot password process:', error);
        // Return error response
        return res.status(500).json({
            status: false,
            message: 'FORGOT_PASSWORD_FAILED',
            meta: {
                error: error.message,
                suggestions: [
                    'Check your input data for errors.',
                    'Ensure the SMS service is running.',
                    'Try again later.',
                ],
            },
        });
    }
};
exports.forgotPassword = forgotPassword;
// Verify Code Function
const verifyCode = async (req, res) => {
    const { phone_number, verificationCode } = req.body;
    // Validate required fields
    if (!phone_number || !verificationCode) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'Phone number and verification code are required.',
                suggestions: ['Provide both phone number and verification code.'],
            },
        });
    }
    const sanitizedPhoneNumber = sanitizeInput(phone_number);
    const sanitizedVerificationCode = sanitizeInput(verificationCode);
    try {
        // Find the user by phone number
        const user = await user_model_1.default.findOne({ phone_number: sanitizedPhoneNumber });
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided phone number does not exist.',
                    suggestions: ['Check the phone number and try again.'],
                },
            });
        }
        // Ensure consistent data types for comparison
        if (String(user.verificationCode) !== sanitizedVerificationCode) {
            return res.status(400).json({
                status: false,
                message: 'INVALID_VERIFICATION_CODE',
                meta: {
                    error: 'The verification code is incorrect.',
                    suggestions: ['Check the code and try again.'],
                },
            });
        }
        await user_model_1.default.updateOne({ _id: user._id }, {
            $set: { userIsVerified: true },
            $unset: { verificationCode: '' },
        });
        // Return success response
        return res.status(200).json({
            status: true,
            message: 'VERIFICATION_CODE_VALID',
            data: {
                phone_number: user.phone_number,
            },
        });
    }
    catch (error) {
        console.error('Error during verification code validation:', error);
        // Return error response
        return res.status(500).json({
            status: false,
            message: 'VERIFICATION_FAILED',
            meta: {
                error: error.message,
                suggestions: [
                    'Check your input data for errors.',
                    'Try again later.',
                ],
            },
        });
    }
};
exports.verifyCode = verifyCode;
// Reset Password Function
const resetPassword = async (req, res) => {
    const { phone_number, newPassword } = req.body;
    // Validate required fields
    if (!phone_number || !newPassword) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'Phone number and new password are required.',
                suggestions: ['Provide all required fields.'],
            },
        });
    }
    const sanitizedPhoneNumber = sanitizeInput(phone_number);
    const sanitizedNewPassword = sanitizeInput(newPassword);
    try {
        // Find the user by phone number
        const user = await user_model_1.default.findOne({ phone_number: sanitizedPhoneNumber });
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided phone number does not exist.',
                    suggestions: ['Check the phone number and try again.'],
                },
            });
        }
        // Hash the new password
        const saltRounds = 10;
        const hashedPassword = await bcrypt_1.default.hash(sanitizedNewPassword, saltRounds);
        // Update the user's password and remove the verification code
        user.password = hashedPassword;
        user.verificationCode = null; // Clear the verification code after use
        await user.save();
        // Return success response
        return res.status(200).json({
            status: true,
            message: 'PASSWORD_RESET_SUCCESSFULLY',
            data: {
                phone_number: user.phone_number,
            },
        });
    }
    catch (error) {
        console.error('Error during password reset process:', error);
        // Return error response
        return res.status(500).json({
            status: false,
            message: 'PASSWORD_RESET_FAILED',
            meta: {
                error: error.message,
                suggestions: [
                    'Check your input data for errors.',
                    'Try again later.',
                ],
            },
        });
    }
};
exports.resetPassword = resetPassword;
