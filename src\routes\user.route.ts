import express from 'express';
import { Router } from 'express';
import { 
  addBeneficiary,
  deletePin,
  getBeneficiaries,
  getUserDetails, 
  setPin, 
  submitBusinessDetails, 
  updatePin, 
  updateProfile, 
  updateProfilePicture,  
  uploadKycDocuments, 
  verifyPaymentCode, 
  verifyPin
} from '../controllers/user.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { upload } from '../services/uploader.service';
import { upgradeAccountToBusiness } from '../controllers/admin.controller';

const userRouter = Router();

// Profile and basic routes
userRouter.get('/me/:userId', authenticateToken, getUserDetails);
userRouter.post('/profile/:userId', authenticateToken, updateProfile);
userRouter.post('/upgrade-to-business/:userId', authenticateToken, upgradeAccountToBusiness);
userRouter.post('/submit-business-details/:userId', authenticateToken, submitBusinessDetails);

// File upload routes using consistent pattern
userRouter.post(
  '/profile-picture/:userId',
  authenticateToken,
  upload.array('profilePicture', 1),
  updateProfilePicture
);

userRouter.post(
  '/kyc/:userId/kyc-documents',
  authenticateToken,
  upload.array('kyc', 5),
  uploadKycDocuments
);


userRouter.post('/:userId/pin/set', authenticateToken, setPin);
userRouter.post('/:userId/pin/verify', authenticateToken, verifyPin);


userRouter.post('/:userId/pin/verify-payment-code', authenticateToken, verifyPaymentCode);

userRouter.put('/:userId/pin/update', authenticateToken, updatePin);
userRouter.delete('/:userId/pin/delete', authenticateToken, deletePin);

userRouter.post('/:userId/addbeneficiaries', authenticateToken, addBeneficiary);
userRouter.get('/:userId/getbeneficiaries', authenticateToken, getBeneficiaries);



export default userRouter;
