"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sortPayments = exports.payoutService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const stripe_1 = __importDefault(require("stripe"));
const transaction_model_1 = require("../models/transaction.model");
const accountnumber_service_1 = require("./accountnumber.service");
const stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY);
// Payout Service
const payoutService = async (userId, amount, payoutMethod, destination, user, account) => {
    const session = await mongoose_1.default.startSession();
    session.startTransaction();
    try {
        // Determine payout provider based on user's country
        let payoutResult;
        switch (user.country_of_residence) {
            case 'US':
                // Process payout via Stripe
                payoutResult = await stripe.transfers.create({
                    amount: amount * 100, // Convert to cents
                    currency: 'usd',
                    destination,
                });
                break;
            case 'NG':
                // Process payout for Nigeria (e.g., Flutterwave)
                // payoutResult = await flutterwave.Transfer.initiate({ ... });
                break;
            case 'LR':
                // Process payout for Liberia (e.g., Flutterwave)
                // payoutResult = await flutterwave.Transfer.initiate({ ... });
                break;
            case 'SL':
                // Process payout for Sierra Leone (e.g., Flutterwave)
                // payoutResult = await flutterwave.Transfer.initiate({ ... });
                break;
            case 'GN':
                // Process payout for Guinea (e.g., Flutterwave)
                // payoutResult = await flutterwave.Transfer.initiate({ ... });
                break;
            default:
                throw new Error('Unsupported country');
        }
        // Deduct amount from user's balance
        const userBalance = account.balance.find((b) => b.currency === 'USD');
        if (!userBalance)
            throw new Error('Currency not found in account balance');
        userBalance.walletBalance -= amount;
        await account.save({ session });
        // Generate a unique transaction ID using SHA-256
        const transactionInput = `${userId}-${amount}-${Date.now()}`;
        const transactionId = (0, accountnumber_service_1.generateTransactionId)(transactionInput);
        // Create a transaction record
        const transaction = new transaction_model_1.Transaction({
            transactionId,
            type: 'WITHDRAW',
            title: `Payout to ${destination}`,
            amount,
            sender: {
                userId: user._id,
                name: user.full_legal_name,
                accountNumber: account.accountNumber,
            },
            customDescription: `Payout via ${payoutMethod}`,
            predefinedDescription: `Payout of ${amount} USD to ${destination}`,
            status: 'SUCCESS',
            timestamp: new Date(),
            receipt: (0, accountnumber_service_1.createTransactionReceipt)('SANGA', 'PAYOUT', new Date().toISOString(), transactionId),
        });
        await transaction.save({ session });
        // Commit transaction
        await session.commitTransaction();
        return {
            status: 'SUCCESS',
            transactionId: transaction.transactionId,
            payoutResult,
        };
    }
    catch (error) {
        // Rollback transaction on error
        await session.abortTransaction();
        throw new Error(`Payout failed: ${error.message}`);
    }
    finally {
        session.endSession();
    }
};
exports.payoutService = payoutService;
// Function to sort payments (example: sort by amount or timestamp)
const sortPayments = (transactions, sortBy, order = 'asc') => {
    return transactions.sort((a, b) => {
        if (sortBy === 'amount') {
            return order === 'asc' ? a.amount - b.amount : b.amount - a.amount;
        }
        else if (sortBy === 'timestamp') {
            return order === 'asc' ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
        }
        return 0;
    });
};
exports.sortPayments = sortPayments;
