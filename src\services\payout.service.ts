import mongoose from 'mongoose';
import crypto from 'crypto';
import <PERSON><PERSON> from 'stripe';
import { Transaction } from '../models/transaction.model';
import { createTransactionReceipt, generateTransactionId } from './accountnumber.service';
import { IUser } from '../models/user.model';
import { IAccount } from '../models/accountnumber.model';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

// Payout Service
export const payoutService = async (
  userId: string,
  amount: number,
  payoutMethod: string,
  destination: string,
  user: IUser,
  account: IAccount
): Promise<any> => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    // Determine payout provider based on user's country
    let payoutResult;
    switch (user.country_of_residence) {
      case 'US':
        // Process payout via Stripe
        payoutResult = await stripe.transfers.create({
          amount: amount * 100, // Convert to cents
          currency: 'usd',
          destination,
        });
        break;

      case 'NG':
        // Process payout for Nigeria (e.g., Flutterwave)
        // payoutResult = await flutterwave.Transfer.initiate({ ... });
        break;

      case 'LR':
        // Process payout for Liberia (e.g., Flutterwave)
        // payoutResult = await flutterwave.Transfer.initiate({ ... });
        break;

      case 'SL':
        // Process payout for Sierra Leone (e.g., Flutterwave)
        // payoutResult = await flutterwave.Transfer.initiate({ ... });
        break;

      case 'GN':
        // Process payout for Guinea (e.g., Flutterwave)
        // payoutResult = await flutterwave.Transfer.initiate({ ... });
        break;

      default:
        throw new Error('Unsupported country');
    }

    // Deduct amount from user's balance
    const userBalance = account.balance.find((b: any) => b.currency === 'USD');
    if (!userBalance) throw new Error('Currency not found in account balance');
    userBalance.walletBalance -= amount;
    await account.save({ session });

    // Generate a unique transaction ID using SHA-256
    const transactionInput = `${userId}-${amount}-${Date.now()}`;
    const transactionId = generateTransactionId(transactionInput);

    // Create a transaction record
    const transaction = new Transaction({
      transactionId,
      type: 'WITHDRAW',
      title: `Payout to ${destination}`,
      amount,
      sender: {
        userId: user._id,
        name: user.full_legal_name,
        accountNumber: account.accountNumber,
      },
      customDescription: `Payout via ${payoutMethod}`,
      predefinedDescription: `Payout of ${amount} USD to ${destination}`,
      status: 'SUCCESS',
      timestamp: new Date(),
      receipt: createTransactionReceipt('SANGA', 'PAYOUT', new Date().toISOString(), transactionId),
    });

    await transaction.save({ session });

    // Commit transaction
    await session.commitTransaction();

    return {
      status: 'SUCCESS',
      transactionId: transaction.transactionId,
      payoutResult,
    };
  } catch (error: any) {
    // Rollback transaction on error
    await session.abortTransaction();
    throw new Error(`Payout failed: ${error.message}`);
  } finally {
    session.endSession();
  }
};

// Function to sort payments (example: sort by amount or timestamp)
export const sortPayments = (transactions: any[], sortBy: 'amount' | 'timestamp', order: 'asc' | 'desc' = 'asc') => {
  return transactions.sort((a, b) => {
    if (sortBy === 'amount') {
      return order === 'asc' ? a.amount - b.amount : b.amount - a.amount;
    } else if (sortBy === 'timestamp') {
      return order === 'asc' ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
    }
    return 0;
  });
};