import { Account, Sequence } from '../models/accountnumber.model';
import { Mutex } from 'async-mutex';
import crypto from 'crypto';

const mutex = new Mutex(); // Create a mutex instance
let currentLength = 10; // Start with 10-digit account numbers

// Generate an account number with dynamic length
const generateAccountNumber = (starter: string, accountID: number): string => {
  const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14); // YYYYMMDDHHMMSS
  const initialValue = `${starter}${accountID}`;

  // Calculate padding length based on current account number length
  const paddingLength = currentLength - 1 - accountID.toString().length - 2;
  const paddedValue = initialValue + timestamp.slice(0, paddingLength);

  // Add a random 2-digit suffix
  const randomSuffix = Math.floor(10 + Math.random() * 90).toString();
  return `${paddedValue}${randomSuffix}`;
};

// Get the next value from a sequence
const getNextSequenceValue = async (sequenceName: string): Promise<number> => {
  const sequence = await Sequence.findOneAndUpdate(
    { name: sequenceName },
    { $inc: { value: 1 } },
    { new: true, upsert: true }
  );

  if (!sequence) {
    throw new Error('Failed to generate sequence value');
  }

  return sequence.value;
};

// Create an account

export const createAccountService = async (
  userId: string,
  account_type: string
): Promise<any> => {
  const release = await mutex.acquire(); // Acquire the mutex lock

  try {
    // Get the next accountID from the sequence
    const sequenceName = `accountID_${currentLength}`;
    let accountID = await getNextSequenceValue(sequenceName);

    // Check if the current length limit is reached
    const maxAccountID = Math.pow(10, currentLength - 3) - 1; // Max AccountID for current length
    if (accountID > maxAccountID) {
      // Expand the account number length
      currentLength += 1;
      accountID = 1; // Reset AccountID for the new length
    }

    // Generate the account number
    const starter = '1'; // Fixed starter for simplicity
    const accountNumber = generateAccountNumber(starter, accountID);

    // Initialize balance for all supported currencies (from your schema)
    const balance = [
      { currency: 'USD', amount: 0 },
      { currency: 'LRD', amount: 0 },
      { currency: 'SLL', amount: 0 },
      { currency: 'GNF', amount: 0 },
    ];

    // Save the account to the database
    const account = new Account({
      accountNumber,
      userId,
      account_type,
      balance, // Store balances in the database as per your schema
      countries: ['USD', 'LRD', 'SLL', 'GNF'], // Default supported countries/currencies
      status: 'active', // Default status
    });

    await account.save();

    return {
      id: account._id,
      account_number: account.accountNumber,
      account_type: account.account_type,
      balance: account.balance, // Ensure correct balance is returned
      countries: account.countries, // Return the supported countries/currencies
      status: account.status,
      created_at: account.created_at,
    };
  } catch (error) {
    console.error('Error during account creation:', error);
    throw error;
  } finally {
    release(); // Release the mutex lock
  }
};


export async function getAccountNumberByUserId(userId: string) {
  try {
    // Query the Account collection for the user's account
    const account = await Account.findOne({ userId: userId }).exec();

    if (!account) {
      console.error('Account not found for user ID:', userId);
      return null;
    }

    // Return the account number
    return account.accountNumber;
  } catch (error) {
    console.error('Error fetching account:', error);
    throw error;
  }
}


// Helper function to generate SHA-256 hashes
export const generateTransactionId = (input: string): string => {
  const hash = crypto.createHash('sha256').update(input).digest('hex');
  return hash.slice(0, 32).toUpperCase(); // Convert to uppercase for consistency
};

// Helper function to create a transaction receipt
export const createTransactionReceipt = (
  prefix: string,
  transactionType: string,
  timestamp: string,
  transactionId: string
): string => {
  return `${prefix}|${transactionType}|${timestamp}|${transactionId}`;
};