import { Account, Sequence } from '../models/accountnumber.model';
import { Mutex } from 'async-mutex';
import crypto from 'crypto';

const mutex = new Mutex();
let currentLength = 10;

const generateAccountNumber = (starter: string, accountID: number): string => {
  const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14);
  const initialValue = `${starter}${accountID}`;


  const paddingLength = currentLength - 1 - accountID.toString().length - 2;
  const paddedValue = initialValue + timestamp.slice(0, paddingLength);


  const randomSuffix = Math.floor(10 + Math.random() * 90).toString();
  return `${paddedValue}${randomSuffix}`;
};


const getNextSequenceValue = async (sequenceName: string): Promise<number> => {
  const sequence = await Sequence.findOneAndUpdate(
    { name: sequenceName },
    { $inc: { value: 1 } },
    { new: true, upsert: true }
  );

  if (!sequence) {
    throw new Error('Failed to generate sequence value');
  }

  return sequence.value;
};



export const createAccountService = async (
  userId: string,
  account_type: string
): Promise<any> => {
  const release = await mutex.acquire();

  try {

    const sequenceName = `accountID_${currentLength}`;
    let accountID = await getNextSequenceValue(sequenceName);

    const maxAccountID = Math.pow(10, currentLength - 3) - 1;
    if (accountID > maxAccountID) {
      currentLength += 1;
      accountID = 1;
    }

    const starter = '1';
    const accountNumber = generateAccountNumber(starter, accountID);

    const balance = [
      { currency: 'USD', amount: 0 },
      { currency: 'LRD', amount: 0 },
      { currency: 'SLL', amount: 0 },
      { currency: 'GNF', amount: 0 },
    ];


    const account = new Account({
      accountNumber,
      userId,
      account_type,
      balance,
      countries: ['USD', 'LRD', 'SLL', 'GNF'],
      status: 'active',
    });

    await account.save();

    return {
      id: account._id,
      account_number: account.accountNumber,
      account_type: account.account_type,
      balance: account.balance,
      countries: account.countries,
      status: account.status,
      created_at: account.created_at,
    };
  } catch (error) {
    console.error('Error during account creation:', error);
    throw error;
  } finally {
    release();
  }
};


export async function getAccountNumberByUserId(userId: string) {
  try {

    const account = await Account.findOne({ userId: userId }).exec();

    if (!account) {
      console.error('Account not found for user ID:', userId);
      return null;
    }


    return account.accountNumber;
  } catch (error) {
    console.error('Error fetching account:', error);
    throw error;
  }
}



export const generateTransactionId = (input: string): string => {
  const hash = crypto.createHash('sha256').update(input).digest('hex');
  return hash.slice(0, 32).toUpperCase();
};


export const createTransactionReceipt = (
  prefix: string,
  transactionType: string,
  timestamp: string,
  transactionId: string
): string => {
  return `${prefix}|${transactionType}|${timestamp}|${transactionId}`;
};