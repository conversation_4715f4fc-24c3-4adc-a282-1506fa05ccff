"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const express_1 = __importDefault(require("express"));
const mongoose_1 = __importDefault(require("mongoose"));
const http_1 = __importDefault(require("http"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const auth_route_1 = __importDefault(require("./routes/auth.route"));
const user_route_1 = __importDefault(require("./routes/user.route"));
const account_route_1 = __importDefault(require("./routes/account.route"));
const callback_route_1 = __importDefault(require("./routes/callback.route"));
const dev_route_1 = __importDefault(require("./routes/dev.route"));
const helper_route_1 = __importDefault(require("./routes/helper.route"));
const admin_route_1 = __importDefault(require("./routes/admin.route"));
const payout_route_1 = __importDefault(require("./routes/payout.route"));
const billpayment_route_1 = __importDefault(require("./routes/billpayment.route"));
dotenv_1.default.config();
const app = (0, express_1.default)();
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)());
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: false }));
const port = process.env.PORT || 5000;
const mongoURI = process.env.MONGODB_URI;
const server = http_1.default.createServer(app);
// Redis client setup
// const redisClient = createClient({
//   url: process.env.REDIS_URL, // Use the REDIS_URL from environment variables
// });
// redisClient.on('error', (err: any) => {
//   console.error('Redis Client Error:', err);
// });
// (async () => {
//   await redisClient.connect();
//   console.log('Connected to Redis');
// })();
// Database connection
const connectToDatabase = async () => {
    try {
        await mongoose_1.default.connect(`${mongoURI}`);
        console.log('Connected to MongoDB');
    }
    catch (error) {
        console.error('Error connecting to MongoDB:', getErrorMessage(error));
        process.exit(1);
    }
};
connectToDatabase();
// Routes
app.use('/api/v1/auth', auth_route_1.default);
app.use('/api/v1/user', user_route_1.default);
app.use('/api/v1/acc', account_route_1.default);
app.use('/api/v1/callback', callback_route_1.default);
app.use('/api/v1/oauth', dev_route_1.default);
app.use('/api/v1/helper', helper_route_1.default);
app.use('/api/v1/enya', payout_route_1.default);
app.use('/api/v1/nebula', admin_route_1.default);
app.use('/api/v1/billpayment', billpayment_route_1.default);
// Base route - Status check for API
app.get('/', (req, res) => {
    res.status(200).json({
        status: true,
        message: 'All systems working fine Alien, add /api/v1 - plus required params.',
    });
});
app.get('/api/v1', (req, res) => {
    res.status(200).json({
        status: true,
        message: 'All systems working fine Alien, Base Url reached - add required params.',
    });
});
// Start the server
server.listen(port, () => {
    console.log(`Server is running on http://localhost:${port}`);
});
function getErrorMessage(error) {
    if (error instanceof Error) {
        return error.message;
    }
    return 'An unknown error occurred';
}
