<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Stripe Payment</title>
  <script src="https://js.stripe.com/v3/"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    #card-element {
      border: 1px solid #ccc;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 20px;
    }
    button {
      background-color: #6772e5;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #5469d4;
    }
    .message {
      margin-top: 20px;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <h1>Pay with Stripe</h1>
  <label for="user-id">User ID:</label>
  <input type="text" id="user-id" placeholder="Enter your User ID" />
  <div id="card-element">
    <!-- Stripe Elements will be inserted here -->
  </div> 
  <button id="submit">Pay Now</button>
  <div id="message" class="message"></div>

  <script>
    // Initialize Stripe with your publishable key
    const stripe = Stripe('pk_test_51QkdIsRoh11lQUTEuw7x6SCTSoEQHHQiRvCRezwuymzrSggQx3shqt7qB0npLhUxMihBQs9Nj7tmgHchHs86m1gt00J4jvz4FX'); // Replace with your publishable key
    const elements = stripe.elements();

    const cardElement = elements.create('card');
    cardElement.mount('#card-element');

    document.querySelector('#submit').addEventListener('click', async () => {
      const amount = 1000000; // $10.00 in cents
      const currency = 'usd';
      const userId = document.querySelector('#user-id').value.trim();

      if (!userId) {
        document.querySelector('#message').textContent = 'User ID is required';
        return;
      }

      document.querySelector('#submit').disabled = true;
      document.querySelector('#message').textContent = 'Processing payment...';

      try {
        // Step 1: Create a PaymentMethod
        const { paymentMethod, error: paymentMethodError } = await stripe.createPaymentMethod({
          type: 'card',
          card: cardElement,
        });

        if (paymentMethodError) {
          throw paymentMethodError;
        }

        console.log(paymentMethod);

        // Step 2: Send PaymentMethod ID to the backend to create and confirm the PaymentIntent
        const response = await fetch('http://localhost:5000/api/v1/acc/fund-usd-balance', {
          method: 'POST',
          headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer 703966_7b22757365724964223a3738393939312c22636c69656e745f6964223a22436c5f35653734636630316361353266643462222c225f657870697279223a332e31353537363030303031373339373335652b32322c227065726d697373696f6e73223a5b2272656164222c227772697465222c22757064617465222c2264656c657465225d2c22746f6b656e54797065223a22616363657373227d.37a9a7e3f0280c7c_60126b1e0e474e4d`,
        }, 
        body: JSON.stringify({
            amount,
            currency,
            paymentMethodId: paymentMethod.id,
            userId,
          }),
        });

        const data = await response.json();

        if (!data.status) {
          throw new Error(data.meta?.error || 'Failed to create PaymentIntent');
        }

        if (data.data.status === 'succeeded') {
          document.querySelector('#message').textContent = `Payment successful! PaymentIntent ID: ${data.data.paymentIntentId}`;
        } else {
          document.querySelector('#message').textContent = `Payment status: ${data.data.status}`;
        }
      } catch (error) {
        console.error('Error:', error);
        document.querySelector('#message').textContent = `Payment failed: ${error.message}`;
      } finally {
        document.querySelector('#submit').disabled = false;
      }
    });
  </script>
</body>
</html> 


