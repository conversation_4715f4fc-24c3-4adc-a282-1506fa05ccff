import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { User } from '../models/user.model';
import { createAccountService } from '../services/accountnumber.service';
import { Account } from '../models/accountnumber.model';
import Queue from 'bull';
import crypto from 'crypto';

import dotenv from 'dotenv';
import { transactionQueue } from '../workers/transactionqueue.worker';

dotenv.config();


const SUPPORTED_ACCOUNT_TYPES = ['savings', 'current', 'business'];

const SUPPORTED_CURRENCIES = ['USD', 'EUR', 'GBP', 'INR'];


export const createAccountNumber = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const { account_type, currency } = req.body;

  // Validate userId
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'The provided userId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid userId and try again.'],
      },
    });
  }

  // Validate account_type
  if (!account_type || !SUPPORTED_ACCOUNT_TYPES.includes(account_type)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_ACCOUNT_TYPE',
      meta: {
        error: 'The provided account type is missing or not supported.',
        suggestions: [`Provide a valid account type: ${SUPPORTED_ACCOUNT_TYPES.join(', ')}.`],
      },
    });
  }

  // Validate currency
  if (!currency || !SUPPORTED_CURRENCIES.includes(currency)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_CURRENCY',
      meta: {
        error: 'The provided currency is missing or not supported.',
        suggestions: [`Provide a valid currency: ${SUPPORTED_CURRENCIES.join(', ')}.`],
      },
    });
  }


  try {
    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        status: false,
        message: 'USER_NOT_FOUND',
        meta: {
          error: 'User with the provided ID does not exist.',
          suggestions: ['Check the user ID and try again.'],
        },
      });
    }

    // Check if the user has the appropriate role for the account type
    if (account_type === 'business' && user.role !== 'Business') {
      return res.status(403).json({
        status: false,
        message: 'UNAUTHORIZED_ACCOUNT_CREATION',
        meta: {
          error: 'Only users with the "Business" role can create business accounts.',
          suggestions: ['Upgrade your account to a business account or choose a different account type.'],
        },
      });
    }

    // Check if the user already has an account of the same type
    const existingAccount = await Account.findOne({ userId, account_type });
    if (existingAccount) {
      return res.status(400).json({
        status: false,
        message: 'ACCOUNT_ALREADY_EXISTS',
        meta: {
          error: 'User already has an account of this type.',
          suggestions: ['Provide a different account type or update the existing account.'],
        },
      });
    }

    //  service to create the account
    const account = await createAccountService(userId, account_type, currency);

    return res.status(201).json({
      status: true,
      message: 'ACCOUNT_CREATED_SUCCESSFULLY',
      data: account,
    });
  } catch (error: any) {
    console.error('Error during account creation:', error);

    return res.status(500).json({
      status: false,
      message: 'ACCOUNT_CREATION_FAILED',
      meta: {
        error: error.message,
        suggestions: [
          'Check your input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};

// Helper function to generate SHA-256 hashes
const generateTransactionId = (input: string): string => {
  return crypto.createHash('sha256').update(input).digest('hex');
};

export const transferMoney = async (req: Request, res: Response): Promise<any> => {
  const { senderUserId, recipientIdentifier, amount, customDescription } = req.body;

  // Validate input
  if (!senderUserId || !mongoose.Types.ObjectId.isValid(senderUserId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_SENDER_ID',
      meta: {
        error: 'The provided senderUserId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid senderUserId and try again.'],
      },
    });
  }

  if (!recipientIdentifier) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_RECIPIENT_IDENTIFIER',
      meta: {
        error: 'Recipient identifier (username or account number) is missing.',
        suggestions: ['Provide a valid recipient identifier and try again.'],
      },
    });
  }

  if (!amount || amount <= 0) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_AMOUNT',
      meta: {
        error: 'The provided amount is missing or invalid.',
        suggestions: ['Provide a valid amount greater than 0 and try again.'],
      },
    });
  }

  // Generate unique inputs for transaction IDs
  const timestamp = Date.now();
  const senderInput = `${senderUserId}-${recipientIdentifier}-${amount}-${timestamp}`;
  const recipientInput = `${recipientIdentifier}-${senderUserId}-${amount}-${timestamp}`;

  // Generate SHA-256 hashes for transaction IDs
  const senderTransactionId = generateTransactionId(senderInput);
  const recipientTransactionId = generateTransactionId(recipientInput);

  // Add the transaction to the queue with transaction IDs
  await transactionQueue.add({
    senderUserId,
    recipientIdentifier,
    amount,
    customDescription,
    senderTransactionId, // Pass sender transaction ID
    recipientTransactionId, // Pass recipient transaction ID
  });

  return res.status(200).json({
    status: true,
    message: 'TRANSACTION_QUEUED',
    data: {
      senderUserId,
      recipientIdentifier,
      amount,
      customDescription,
      senderTransactionId, // Return sender transaction ID in response
      recipientTransactionId, // Return recipient transaction ID in response
    },
  });
};