"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const accountnumber_controller_1 = require("../controllers/accountnumber.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const stripe_controller_1 = require("../controllers/stripe.controller");
const stripe_service_1 = require("../services/stripe.service");
const exchange_controller_1 = require("../controllers/exchange.controller");
const stripeService = new stripe_service_1.StripeService();
const accountRouter = express_1.default.Router();
accountRouter.post('/create-account-number/:userId', auth_middleware_1.authenticateToken, accountnumber_controller_1.createAccountNumber);
accountRouter.post('/intra-transfer', auth_middleware_1.authenticateToken, accountnumber_controller_1.intraTransfer);
accountRouter.get('/fetch-transactions/:userId', auth_middleware_1.authenticateToken, accountnumber_controller_1.aggregateUserTransactions);
accountRouter.get('/get-account-name/:accountNumber', accountnumber_controller_1.getUserInfoByAccountNumber);
accountRouter.post('/create-usd-cardholder', auth_middleware_1.authenticateToken, stripe_controller_1.createCardholder);
accountRouter.post('/create-credit-card', auth_middleware_1.authenticateToken, stripe_controller_1.issueCreditCard);
accountRouter.post('/create-stripe-account', auth_middleware_1.authenticateToken, stripe_controller_1.createStripeAccount);
accountRouter.post('/link-card-to-stripe-account', auth_middleware_1.authenticateToken, stripe_controller_1.linkCard);
accountRouter.get('/fetch-credit-card/:userId', auth_middleware_1.authenticateToken, stripe_controller_1.retrieveCreditCard);
accountRouter.post('/swap', auth_middleware_1.authenticateToken, exchange_controller_1.swap);
accountRouter.get('/fetch-wallet-balance/:userId', auth_middleware_1.authenticateToken, accountnumber_controller_1.getWalletData);
accountRouter.post('/create-and-fetch-creditcard', auth_middleware_1.authenticateToken, stripe_controller_1.createCardholderIssueAndRetrieveCard);
accountRouter.get('/fetch-creditcard/:userId/account/:accountNumber', auth_middleware_1.authenticateToken, stripe_controller_1.retrieveAndDecryptCardDetails);
accountRouter.post('/fund-usd-balance', auth_middleware_1.authenticateToken, stripe_controller_1.createPaymentIntent);
accountRouter.post('/stripe/webhook', express_1.default.raw({ type: 'application/json' }), stripe_controller_1.handleStripeWebhook);
exports.default = accountRouter;
