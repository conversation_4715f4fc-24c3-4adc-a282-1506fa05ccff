import * as faceapi from "face-api.js";
import * as tf from "@tensorflow/tfjs-node"; // TensorFlow.js for Node.js
import sharp from "sharp"; // Sharp for image processing
import * as path from "path";

const MODEL_URL = path.join(__dirname, "../aimodels");

export class KYCService {
  constructor() {
    this.loadModels();
  }

  private async loadModels() {
    await faceapi.nets.faceRecognitionNet.loadFromDisk(MODEL_URL);
    await faceapi.nets.faceLandmark68Net.loadFromDisk(MODEL_URL);
    await faceapi.nets.ssdMobilenetv1.loadFromDisk(MODEL_URL);
  }

  private async extractFace(imagePath: string) {
    const imageBuffer = await sharp(imagePath)
      .resize(224, 224) 
      .toBuffer();

    const imageTensor = tf.node.decodeImage(imageBuffer) as tf.Tensor3D;
    const [height, width] = imageTensor.shape;
    const imageData = await tf.browser.toPixels(imageTensor); 
    const imageDataObj = new ImageData(
      new Uint8ClampedArray(imageData),
      width,
      height
    );


    const canvas = document.createElement("canvas");
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext("2d");
    if (!ctx) {
      throw new Error("Could not get canvas context.");
    }
    ctx.putImageData(imageDataObj, 0, 0);

    const detections = await faceapi.detectSingleFace(canvas)
      .withFaceLandmarks()
      .withFaceDescriptor();

    imageTensor.dispose();

    return detections;
  }

  // Compare two faces
  private async compareFaces(
    faceDescriptor1: Float32Array, // Use Float32Array instead of FaceDescriptor
    faceDescriptor2: Float32Array
  ): Promise<boolean> {
    const distance = faceapi.euclideanDistance(faceDescriptor1, faceDescriptor2);
    return distance < 0.6; // Adjust threshold as needed
  }

  // Verify KYC by comparing multiple selfie images with the picture extracted from the ID card
  public async verifyKYC(
    selfiePaths: { front: string; left: string; right: string; up: string; down: string },
    idCardImagePath: string
  ): Promise<boolean> {
    try {

     const idCardDescriptor = await this.extractFace(idCardImagePath);
      if (!idCardDescriptor) {
        throw new Error("No face detected in the ID card image.");
      }

      // Extract faces from all selfie images
      const selfieDescriptors = await Promise.all([
        this.extractFace(selfiePaths.front),
        this.extractFace(selfiePaths.left),
        this.extractFace(selfiePaths.right),
        this.extractFace(selfiePaths.up),
        this.extractFace(selfiePaths.down),
      ]);

      // Check if faces were detected in all selfie images
      if (selfieDescriptors.some((desc) => !desc)) {
        throw new Error("No face detected in one or more selfie images.");
      }

      // Compare each selfie with the ID card picture
      const comparisons = await Promise.all(
        selfieDescriptors.map((desc) =>
          this.compareFaces(desc!.descriptor, idCardDescriptor.descriptor)
        )
      );

      return comparisons.every((match) => match);
    } catch (error) {
      console.error("Error during KYC verification:", error);
      throw error;
    }
  }
}