"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_controller_1 = require("../controllers/auth.controller");
const authRouter = express_1.default.Router();
authRouter.post('/register', auth_controller_1.registerUser);
authRouter.post('/register-public-key', auth_controller_1.storePublicKey);
authRouter.post('/login', auth_controller_1.loginUser);
authRouter.post('/request-login-code', auth_controller_1.requestLoginCode);
authRouter.post('/verify-login-code', auth_controller_1.verifyLoginCode);
authRouter.post('/generate-nonce', auth_controller_1.generateNonceAPI);
authRouter.post('/verify-signature', auth_controller_1.verifyNonceAndLogin);
authRouter.post('/verify-number', auth_controller_1.verifyUser);
authRouter.post('/request-login-code', auth_controller_1.forgotPassword);
authRouter.post('/verify-login-code', auth_controller_1.verifyCode);
authRouter.post('/resend-verification-code', auth_controller_1.resendVerificationCode);
authRouter.post('/reset-password', auth_controller_1.resetPassword);
authRouter.post("/generate-key-pair", auth_controller_1.generateAndStoreKeyPair);
exports.default = authRouter;
