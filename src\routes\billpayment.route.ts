import express from 'express';
import { mobileMoneyCallback } from '../callbacks/mobilemoney';
import { lookupAirtimeOperator, purchaseAirtime } from '../controllers/billpayment.controller';

const billpaymentRouter = express.Router();

billpaymentRouter.post('/mobile-money', mobileMoneyCallback); 
billpaymentRouter.get('/airtime/operator', lookupAirtimeOperator);
billpaymentRouter.post('/airtime/purchase', purchaseAirtime);
// billpaymentRouter.post('/airtime/webhook', handleAirtimeWebhook);

export default billpaymentRouter;
