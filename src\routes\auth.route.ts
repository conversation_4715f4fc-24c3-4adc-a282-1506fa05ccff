import express from 'express';
import { forgotPassword, 
    generateAndStoreKeyPair,
     generateNonceAPI,
      loginUser,
       verifyNonceAndLogin,
        registerUser,
         requestLoginCode,
          resendVerificationCode,
           resetPassword,
            storePublicKey,
             verifyCode,
              verifyLoginCode,
                verifyUser } from '../controllers/auth.controller';

const authRouter = express.Router();

authRouter.post('/register', registerUser); 
authRouter.post('/register-public-key', storePublicKey); 


authRouter.post('/login', loginUser); 


authRouter.post('/request-login-code', requestLoginCode); 
authRouter.post('/verify-login-code', verifyLoginCode); 

authRouter.post('/generate-nonce', generateNonceAPI);

authRouter.post('/verify-signature', verifyNonceAndLogin);





authRouter.post('/verify-number', verifyUser); 
authRouter.post('/request-login-code', forgotPassword); 
authRouter.post('/verify-login-code', verifyCode); 
authRouter.post('/resend-verification-code', resendVerificationCode); 

authRouter.post('/reset-password', resetPassword); 


authRouter.post("/generate-key-pair", generateAndStoreKeyPair);

export default authRouter;
