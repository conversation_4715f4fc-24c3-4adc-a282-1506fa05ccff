"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReloadlyService = void 0;
const axios_1 = __importDefault(require("axios"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const crypto = __importStar(require("crypto"));
const dotenv = __importStar(require("dotenv"));
dotenv.config();
class ReloadlyAuth {
    constructor() {
        this.clientId = process.env.RELOADLY_CLIENT_ID || '';
        this.clientSecret = process.env.RELOADLY_CLIENT_SECRET || '';
        this.audience = process.env.RELOADLY_AUDIENCE || 'https://topups-sandbox.reloadly.com';
        this.tokenUrl = process.env.RELOADLY_ENV === 'production'
            ? 'https://auth.reloadly.com/oauth/token'
            : 'https://auth.reloadly.com/oauth/token';
        this.tokenFilePath = path.join(__dirname, 'reloadly_token.json');
        this.httpClient = axios_1.default.create();
    }
    async saveTokenToFile(token, expiresIn) {
        const expiryTime = new Date();
        expiryTime.setSeconds(expiryTime.getSeconds() + expiresIn);
        const tokenData = {
            accessToken: token,
            expiryTime: expiryTime.toISOString()
        };
        await fs.promises.writeFile(this.tokenFilePath, JSON.stringify(tokenData, null, 2));
    }
    async readTokenFromFile() {
        try {
            const data = await fs.promises.readFile(this.tokenFilePath, 'utf-8');
            return JSON.parse(data);
        }
        catch {
            return null;
        }
    }
    async getAccessToken() {
        const tokenData = await this.readTokenFromFile();
        if (tokenData && new Date(tokenData.expiryTime) > new Date()) {
            return tokenData.accessToken;
        }
        try {
            const response = await this.httpClient.post(this.tokenUrl, {
                client_id: this.clientId,
                client_secret: this.clientSecret,
                grant_type: 'client_credentials',
                audience: this.audience
            }, {
                headers: { 'Content-Type': 'application/json' }
            });
            await this.saveTokenToFile(response.data.access_token, response.data.expires_in);
            return response.data.access_token;
        }
        catch (error) {
            console.error('Error fetching access token:', error);
            throw error;
        }
    }
}
class ReloadlyService {
    constructor() {
        this.authService = new ReloadlyAuth();
        this.baseUrl = process.env.RELOADLY_ENV === 'production'
            ? 'https://topups.reloadly.com'
            : 'https://topups-sandbox.reloadly.com';
        this.httpClient = axios_1.default.create();
    }
    async getAuthHeaders() {
        const accessToken = await this.authService.getAccessToken();
        return {
            'Authorization': `Bearer ${accessToken}`,
            'Accept': 'application/com.reloadly.topups-v1+json',
            'Content-Type': 'application/json'
        };
    }
    generateCustomIdentifier() {
        return `tx_${crypto.randomBytes(12).toString('hex')}_${Date.now()}`;
    }
    async detectOperator(phoneNumber, countryCode) {
        try {
            const headers = await this.getAuthHeaders();
            const response = await this.httpClient.get(`${this.baseUrl}/operators/auto-detect/phone/${phoneNumber}/countries/${countryCode}`, { headers });
            return response.data;
        }
        catch (error) {
            console.error('Operator detection failed:', error);
            throw error;
        }
    }
    async sendTopUp(operatorId, amount, recipientPhone, countryCode, options = {}) {
        try {
            const headers = await this.getAuthHeaders();
            const payload = {
                operatorId,
                amount,
                useLocalAmount: options.useLocalAmount ?? true,
                customIdentifier: this.generateCustomIdentifier(),
                recipientPhone: {
                    countryCode,
                    number: recipientPhone
                },
                ...(options.recipientEmail && { recipientEmail: options.recipientEmail }),
                ...(options.senderPhone && {
                    senderPhone: {
                        countryCode: options.senderPhone.substring(0, 2),
                        number: options.senderPhone.substring(2)
                    }
                })
            };
            const response = await this.httpClient.post(`${this.baseUrl}/topups`, payload, { headers });
            return response.data;
        }
        catch (error) {
            console.error('Top-up failed:', error);
            throw error;
        }
    }
    async processTopUp(phoneNumber, countryCode, amount, options = {}) {
        try {
            const operator = await this.detectOperator(phoneNumber, countryCode);
            return await this.sendTopUp(operator.operatorId, amount, phoneNumber, countryCode, options);
        }
        catch (error) {
            console.error('Top-up process failed:', error);
            throw error;
        }
    }
}
exports.ReloadlyService = ReloadlyService;
