const stripe = require('stripe')(''); 

async function createPaymentMethod(cardDetails) {
  try {

    const paymentMethod = await stripe.paymentMethods.create({
      type: 'card',
      card: cardDetails,
    });

    console.log('Payment Method Created:', paymentMethod.id); 
  } catch (error) {
    console.error('Error:', error);
  }
}


const exampleCardDetails = {
  number: '****************',
  exp_month: 12,
  exp_year: 2025,
  cvc: '123',
};

createPaymentMethod(exampleCardDetails);
