import axios, { AxiosInstance, AxiosResponse } from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import * as dotenv from 'dotenv';

dotenv.config();

class ReloadlyAuth {
  private clientId: string;
  private clientSecret: string;
  private audience: string;
  private tokenUrl: string;
  private httpClient: AxiosInstance;
  private tokenFilePath: string;

  constructor() {
    this.clientId = process.env.RELOADLY_CLIENT_ID || '';
    this.clientSecret = process.env.RELOADLY_CLIENT_SECRET || '';
    this.audience = process.env.RELOADLY_AUDIENCE || 'https://topups-sandbox.reloadly.com';
    this.tokenUrl = process.env.RELOADLY_ENV === 'production' 
      ? 'https://auth.reloadly.com/oauth/token' 
      : 'https://auth.reloadly.com/oauth/token';
    this.tokenFilePath = path.join(__dirname, 'reloadly_token.json');
    this.httpClient = axios.create();
  }

  private async saveTokenToFile(token: string, expiresIn: number): Promise<void> {
    const expiryTime = new Date();
    expiryTime.setSeconds(expiryTime.getSeconds() + expiresIn);
    
    const tokenData = {
      accessToken: token,
      expiryTime: expiryTime.toISOString()
    };

    await fs.promises.writeFile(this.tokenFilePath, JSON.stringify(tokenData, null, 2));
  }

  private async readTokenFromFile(): Promise<{ accessToken: string; expiryTime: string } | null> {
    try {
      const data = await fs.promises.readFile(this.tokenFilePath, 'utf-8');
      return JSON.parse(data);
    } catch {
      return null;
    }
  }

  public async getAccessToken(): Promise<string> {
    const tokenData = await this.readTokenFromFile();
    
    if (tokenData && new Date(tokenData.expiryTime) > new Date()) {
      return tokenData.accessToken;
    }

    try {
      const response = await this.httpClient.post<{
        access_token: string;
        expires_in: number;
        token_type: string;
        scope: string;
      }>(this.tokenUrl, {
        client_id: this.clientId,
        client_secret: this.clientSecret,
        grant_type: 'client_credentials',
        audience: this.audience
      }, {
        headers: { 'Content-Type': 'application/json' }
      });

      await this.saveTokenToFile(response.data.access_token, response.data.expires_in);
      return response.data.access_token;
    } catch (error) {
      console.error('Error fetching access token:', error);
      throw error;
    }
  }
}

export class ReloadlyService {
  private authService: ReloadlyAuth;
  private httpClient: AxiosInstance;
  private baseUrl: string;

  constructor() {
    this.authService = new ReloadlyAuth();
    this.baseUrl = process.env.RELOADLY_ENV === 'production'
      ? 'https://topups.reloadly.com'
      : 'https://topups-sandbox.reloadly.com';
    this.httpClient = axios.create();
  }

  private async getAuthHeaders() {
    const accessToken = await this.authService.getAccessToken();
    return {
      'Authorization': `Bearer ${accessToken}`,
      'Accept': 'application/com.reloadly.topups-v1+json',
      'Content-Type': 'application/json'
    };
  }

  private generateCustomIdentifier() {
    return `tx_${crypto.randomBytes(12).toString('hex')}_${Date.now()}`;
  }

  public async detectOperator(phoneNumber: string, countryCode: string) {
    try {
      const headers = await this.getAuthHeaders();
      const response = await this.httpClient.get(
        `${this.baseUrl}/operators/auto-detect/phone/${phoneNumber}/countries/${countryCode}`,
        { headers }
      );
      return response.data;
    } catch (error) {
      console.error('Operator detection failed:', error);
      throw error;
    }
  }

  public async sendTopUp(
    operatorId: number,
    amount: number,
    recipientPhone: string,
    countryCode: string,
    options: {
      useLocalAmount?: boolean;
      recipientEmail?: string;
      senderPhone?: string;
    } = {}
  ) {
    try {
      const headers = await this.getAuthHeaders();
      
      const payload = {
        operatorId,
        amount,
        useLocalAmount: options.useLocalAmount ?? true,
        customIdentifier: this.generateCustomIdentifier(),
        recipientPhone: {
          countryCode,
          number: recipientPhone
        },
        ...(options.recipientEmail && { recipientEmail: options.recipientEmail }),
        ...(options.senderPhone && { 
          senderPhone: {
            countryCode: options.senderPhone.substring(0, 2),
            number: options.senderPhone.substring(2)
          }
        })
      };

      const response = await this.httpClient.post(
        `${this.baseUrl}/topups`,
        payload,
        { headers }
      );

      return response.data;
    } catch (error) {
      console.error('Top-up failed:', error);
      throw error;
    }
  }

  public async processTopUp(
    phoneNumber: string,
    countryCode: string,
    amount: number,
    options: {
      useLocalAmount?: boolean;
      recipientEmail?: string;
      senderPhone?: string;
    } = {}
  ) {
    try {
      const operator = await this.detectOperator(phoneNumber, countryCode);
      return await this.sendTopUp(
        operator.operatorId,
        amount,
        phoneNumber,
        countryCode,
        options
      );
    } catch (error) {
      console.error('Top-up process failed:', error);
      throw error;
    }
  }
}