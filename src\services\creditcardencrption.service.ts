import * as crypto from 'crypto';
import * as dotenv from 'dotenv';

dotenv.config();

class CustomEncryptionService {
  private readonly SECRET_KEY: string;
  private readonly PBKDF2_ITERATIONS = 100000; // Number of iterations for key stretching
  private readonly PBKDF2_KEY_LENGTH = 32; // 32 bytes = 256 bits
  private readonly PBKDF2_SALT_LENGTH = 16; // 16 bytes = 128 bits
  private readonly ENCRYPTION_ALGORITHM = 'aes-256-cbc';
  private readonly HMAC_ALGORITHM = 'sha256';
  private readonly HMAC_KEY_LENGTH = 32; // 32 bytes = 256 bits
  private readonly PAYLOAD_EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes in milliseconds

  constructor() {
    if (!process.env.SECRET_KEY) {
      throw new Error('SECRET_KEY is not defined in the environment variables.');
    }
    this.SECRET_KEY = process.env.SECRET_KEY;
  }

  /**
   * Generate a secure random 6-digit user ID.
   * @returns A 6-digit number.
   */
  private generateSecureRandomUserId(): number {
    const min = 100000; // Smallest 6-digit number
    const max = 999999; // Largest 6-digit number
    return crypto.randomInt(min, max + 1); // Inclusive of max
  }

  /**
   * Generate a random salt.
   * @returns A 16-character hexadecimal string.
   */
  private generateSalt(): string {
    return crypto.randomBytes(8).toString('hex'); // 8 bytes = 16 characters
  }

  /**
   * Derive a key using PBKDF2.
   * @param password - The password to derive the key from.
   * @param salt - The salt to use for key derivation.
   * @returns A derived key as a buffer.
   */
  private deriveKey(password: string, salt: Buffer): Buffer {
    return crypto.pbkdf2Sync(password, salt, this.PBKDF2_ITERATIONS, this.PBKDF2_KEY_LENGTH, 'sha256');
  }

  /**
   * Create a secure HMAC for data integrity.
   * @param data - The data to create an HMAC for.
   * @param key - The key to use for HMAC.
   * @returns A 32-character hexadecimal HMAC.
   */
  private createHMAC(data: string, key: Buffer): string {
    const hmac = crypto.createHmac(this.HMAC_ALGORITHM, key);
    hmac.update(data);
    return hmac.digest('hex');
  }

  /**
   * Encrypt data using a custom encryption scheme with advanced security features.
   * @param data - The data to encrypt.
   * @returns The encrypted payload in the format "userId_encodedPayload.salt_checksum".
   */
  public encrypt(data: string): string {
    const userId = this.generateSecureRandomUserId(); // Generate a random 6-digit user ID
    const salt = crypto.randomBytes(this.PBKDF2_SALT_LENGTH); // Generate a random salt
    const derivedKey = this.deriveKey(this.SECRET_KEY, salt); // Derive a key using PBKDF2

    // Add random padding to the data
    const padding = crypto.randomBytes(16).toString('hex');
    const paddedData = `${data}.${padding}`;

    // Encrypt the padded data using AES-256-CBC
    const iv = crypto.randomBytes(16); // Generate a random IV
    const cipher = crypto.createCipheriv(this.ENCRYPTION_ALGORITHM, derivedKey, iv);
    let encrypted = cipher.update(paddedData, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Create a timestamp for expiry
    const timestamp = Date.now();

    // Combine userId, encrypted data, IV, salt, and timestamp
    const userIdAndPayload = `${userId}_${encrypted}.${iv.toString('hex')}.${salt.toString('hex')}.${timestamp}`;

    // Create an HMAC for data integrity
    const hmac = this.createHMAC(userIdAndPayload, derivedKey);

    // Combine all parts to create the encrypted payload
    const encryptedPayload = `${userIdAndPayload}.${hmac}`;
    return encryptedPayload;
  }

  /**
   * Decrypt data using the custom encryption scheme.
   * @param encryptedPayload - The encrypted payload in the format "userId_encodedPayload.salt_checksum".
   * @returns The decrypted data.
   */
  public decrypt(encryptedPayload: string): string {
    try {
      if (!encryptedPayload) {
        throw new Error('Encrypted payload is missing.');
      }
  
      // Split the encrypted payload into userId_encryptedData, iv, salt, timestamp, and hmac
      const [userIdAndEncryptedData, ivHex, saltHex, timestamp, hmac] = encryptedPayload.split('.');
      if (!userIdAndEncryptedData || !ivHex || !saltHex || !timestamp || !hmac) {
        throw new Error('Invalid encrypted payload format.');
      }
  
      // Split userId and encryptedData
      const [userId, encryptedData] = userIdAndEncryptedData.split('_');
      if (!userId || !encryptedData) {
        throw new Error('Invalid encrypted payload: Missing userId or encryptedData.');
      }
  
      // Convert IV and salt to buffers
      const iv = Buffer.from(ivHex, 'hex');
      const salt = Buffer.from(saltHex, 'hex');
  
      // Derive the key using PBKDF2
      const derivedKey = this.deriveKey(this.SECRET_KEY, salt);
  
      // Verify the HMAC for data integrity
      const expectedHMAC = this.createHMAC(`${userId}_${encryptedData}.${ivHex}.${saltHex}.${timestamp}`, derivedKey);
      if (!crypto.timingSafeEqual(Buffer.from(hmac, 'hex'), Buffer.from(expectedHMAC, 'hex'))) {
        throw new Error('Invalid encrypted payload: HMAC mismatch.');
      }
  
      // Decrypt the data using AES-256-CBC
      const decipher = crypto.createDecipheriv(this.ENCRYPTION_ALGORITHM, derivedKey, iv);
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
  
      // Remove the padding from the decrypted data
      const [data] = decrypted.split('.');
      return data;
    } catch (error) {
      console.error('Decryption failed:', (error as Error).message);
      throw error;
    }
  }
}

export default CustomEncryptionService;