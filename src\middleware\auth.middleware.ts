// import { Request, Response, NextFunction } from 'express';
// import TokenService from '../apisystem/service/token.service';
// import mongoose from 'mongoose';
// import User from '../models/user.model';

// interface DecodedToken {
//   userId: number;
//   payload: Record<string, any>;
//   permissions: string[];
//   expirationTime: number;
//   tokenType: 'grant' | 'access' | 'refresh';
//   client_id: string; // Ensure client_id exists in token
// }

// declare module 'express' {
//   interface Request {
//     user?: DecodedToken;
//   }
// }

// export const authenticateToken = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
//   try {
//     // Extract the Bearer token from the Authorization header
//     const authHeader = req.headers.authorization;
//     if (!authHeader || !authHeader.startsWith('Bearer ')) {
//       return res.status(401).json({
//         status: false,
//         message: 'UNAUTHORIZED',
//         meta: {
//           error: 'Bearer token is missing or invalid.',
//           suggestions: ['Provide a valid Bearer token in the Authorization header.'],
//         },
//       });
//     }

//     const token = authHeader.split(' ')[1];

//     // Validate the token
//     const tokenService = new TokenService();
//     const decodedToken: DecodedToken | null = tokenService.validateGrantToken(token);
//     if (!decodedToken) {
//       return res.status(401).json({
//         status: false,
//         message: 'UNAUTHORIZED',
//         meta: {
//           error: 'Invalid or expired token.',
//           suggestions: ['Provide a valid token.'],
//         },
//       });
//     }

//     // Extract client_id from the decoded token
//     const { client_id } = decodedToken;
//     if (!client_id) {
//       return res.status(401).json({
//         status: false,
//         message: 'UNAUTHORIZED',
//         meta: {
//           error: 'Client ID is missing in the token.',
//           suggestions: ['Ensure the token contains a valid client_id.'],
//         },
//       });
//     }

//     // Extract userId from request (body, params, or query)
//     const userId = req.body.userId || req.params.userId || req.query.userId;
//     if (!userId) {
//       return res.status(400).json({
//         status: false,
//         message: 'BAD REQUEST',
//         meta: {
//           error: 'User ID is required in the request.',
//           suggestions: ['Provide a valid userId in the request body, params, or query.'],
//         },
//       });
//     }

//     // Validate userId format
//     if (!mongoose.Types.ObjectId.isValid(userId)) {
//       return res.status(400).json({
//         status: false,
//         message: 'BAD REQUEST',
//         meta: {
//           error: 'Invalid user ID format.',
//           suggestions: ['Provide a valid MongoDB ObjectId for userId.'],
//         },
//       });
//     }

//     // Query the User model using userId
//     const user = await User.findById(userId);
//     if (!user) {
//       return res.status(404).json({
//         status: false,
//         message: 'NOT FOUND',
//         meta: {
//           error: 'User not found.',
//           suggestions: ['Ensure the userId provided is correct and exists in the system.'],
//         },
//       });
//     }

//     // Compare client_id from token with client_id in the database
//     if (user.client_id !== client_id) {
//       return res.status(403).json({
//         status: false,
//         message: 'FORBIDDEN',
//         meta: {
//           error: 'Client ID mismatch.',
//           suggestions: ['Login to genrate fresh token for the user.'],
//         },
//       });
//     }

//     // Attach the decoded token and user info to the request object
//     req.user = decodedToken;

//     next();
//   } catch (error) {
//     return res.status(500).json({
//       status: false,
//       message: 'INTERNAL_SERVER_ERROR',
//       meta: {
//         error: error,
//       },
//     });
//   }
// };

import { Request, Response, NextFunction } from 'express';
import TokenService from '../apisystem/service/token.service';
import mongoose from 'mongoose';
import User from '../models/user.model';

interface DecodedToken {
  userId: number;
  payload: Record<string, any>;
  permissions: string[];
  expirationTime: number;
  tokenType: 'grant' | 'access' | 'refresh';
  client_id: string; // Ensure client_id exists in token
}

declare module 'express' {
  interface Request {
    user?: DecodedToken;
  }
}

export const authenticateToken = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
  try {
    // Extract the Bearer token from the Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        status: false,
        message: 'UNAUTHORIZED',
        meta: {
          error: 'Bearer token is missing or invalid.',
          suggestions: ['Provide a valid Bearer token in the Authorization header.'],
        },
      });
    }

    const token = authHeader.split(' ')[1];

    // Validate the token (Temporarily allowing invalid/expired tokens)
    const tokenService = new TokenService();
    const decodedToken: DecodedToken | null = tokenService.validateGrantToken(token);
    
    // if (!decodedToken) {
    //   return res.status(401).json({
    //     status: false,
    //     message: 'UNAUTHORIZED',
    //     meta: {
    //       error: 'Invalid or expired token.',
    //       suggestions: ['Provide a valid token.'],
    //     },
    //   });
    // }

    // Extract client_id from the decoded token
    const client_id = decodedToken?.client_id; // Allowing missing client_id for now

    // Extract userId from request (body, params, or query)
    const userId = req.body.userId || req.params.userId || req.query.userId;
    if (!userId) {
      return res.status(400).json({
        status: false,
        message: 'BAD REQUEST',
        meta: {
          error: 'User ID is required in the request.',
          suggestions: ['Provide a valid userId in the request body, params, or query.'],
        },
      });
    }

    // Validate userId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({
        status: false,
        message: 'BAD REQUEST',
        meta: {
          error: 'Invalid user ID format.',
          suggestions: ['Provide a valid MongoDB ObjectId for userId.'],
        },
      });
    }

    // Query the User model using userId
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        status: false,
        message: 'NOT FOUND',
        meta: {
          error: 'User not found.',
          suggestions: ['Ensure the userId provided is correct and exists in the system.'],
        },
      });
    }

    // Compare client_id from token with client_id in the database (Temporarily bypassing check)
    // if (user.client_id !== client_id) {
    //   return res.status(403).json({
    //     status: false,
    //     message: 'FORBIDDEN',
    //     meta: {
    //       error: 'Client ID mismatch.',
    //       suggestions: ['Login to generate a fresh token for the user.'],
    //     },
    //   });
    // }

    // Attach the decoded token and user info to the request object (even if invalid/expired)
    req.user = decodedToken || ({} as DecodedToken);

    next();
  } catch (error) {
    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: error,
      },
    });
  }
};
