"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const crypto = __importStar(require("crypto"));
const dotenv = __importStar(require("dotenv"));
dotenv.config();
class TokenService {
    constructor() {
        this.INTERNAL_CALL_KEY = 'INTERNAL_CALL'; // Internal identifier
        // 1 trillion years in milliseconds (effectively infinite)
        this.ONE_TRILLION_YEARS_IN_MS = 1e12 * 365.25 * 24 * 60 * 60 * 1000;
        if (!process.env.SECRET_KEY) {
            throw new Error('SECRET_KEY is not defined in the environment variables.');
        }
        this.SECRET_KEY = process.env.SECRET_KEY;
    }
    // Generate a secure random 6-digit user ID
    generateSecureRandomUserId() {
        const min = 100000; // Smallest 6-digit number
        const max = 999999; // Largest 6-digit number
        return crypto.randomInt(min, max + 1); // Inclusive of max
    }
    // Generate a random salt
    generateSalt() {
        return crypto.randomBytes(8).toString('hex'); // 8 bytes = 16 characters
    }
    // Create a secure checksum using HMAC-SHA256
    createChecksum(data) {
        const hmac = crypto.createHmac('sha256', this.SECRET_KEY);
        hmac.update(data);
        return hmac.digest('hex').substring(0, 16); // Truncate to 32 characters
    }
    // Generate a grant token with expiration time and permissions embedded in the payload
    generateGrantToken(payload, expiresIn, permissions = [], internalCallKey, // Optional internal call identifier
    tokenType = 'grant' // Default to 'grant' token type
    ) {
        // If this is an internal call, bypass expiration time restrictions
        const isInternalCall = internalCallKey === this.INTERNAL_CALL_KEY;
        // Ensure expiresIn is greater than 0 unless it's an internal call
        if (!isInternalCall && expiresIn <= 0) {
            throw new Error('expiresIn is required and must be greater than 0.');
        }
        else if (!isInternalCall && expiresIn > 600) {
            throw new Error('expiresIn must be less than or equal to 10 minutes (600 seconds).');
        }
        const userId = this.generateSecureRandomUserId(); // Generate a random 6-digit user ID
        const timestamp = Date.now(); // Current UTC timestamp in milliseconds
        // Calculate expiration time
        const expirationTime = timestamp + expiresIn * 1000; // Expiration time in UTC
        // Merge payload, permissions, expiration time, and token type
        const payloadWithExpiry = { ...payload, _expiry: expirationTime, permissions, tokenType };
        // Encode the payload as a hexadecimal string
        const encodedPayloadWithExpiry = Buffer.from(JSON.stringify(payloadWithExpiry)).toString('hex');
        // Combine userId and encoded payload
        const userIdAndPayload = `${userId}_${encodedPayloadWithExpiry}`;
        // Generate a salt
        const salt = this.generateSalt();
        // Create a checksum for the combined userId, payload, and salt
        const checksum = this.createChecksum(`${userIdAndPayload}.${salt}`);
        // Combine salt and checksum
        const saltAndChecksum = `${salt}_${checksum}`;
        // Combine all parts to create the token
        const token = `${userIdAndPayload}.${saltAndChecksum}`;
        return token;
    }
    // Validate and decode the grant token
    validateGrantToken(token) {
        try {
            if (!token) {
                throw new Error('Token is missing');
            }
            const tokenParts = token.split('.');
            if (tokenParts.length !== 2) {
                throw new Error('Invalid token');
            }
            const [userIdAndPayload, saltAndChecksum] = tokenParts;
            // Split userId and payload
            const [userId, encodedPayloadWithExpiry] = userIdAndPayload.split('_');
            if (!userId || !encodedPayloadWithExpiry) {
                throw new Error('Invalid token: Missing userId or payload');
            }
            // Split salt and checksum
            const [salt, checksum] = saltAndChecksum.split('_');
            if (!salt || !checksum) {
                throw new Error('Invalid token: Missing salt or checksum');
            }
            // Verify the checksum
            const expectedChecksum = this.createChecksum(`${userId}_${encodedPayloadWithExpiry}.${salt}`);
            if (!crypto.timingSafeEqual(Buffer.from(checksum, 'hex'), Buffer.from(expectedChecksum, 'hex'))) {
                throw new Error('Invalid token: Checksum mismatch');
            }
            // Decode the payload with expiry
            let payloadWithExpiry;
            try {
                // Decode the hexadecimal string
                payloadWithExpiry = JSON.parse(Buffer.from(encodedPayloadWithExpiry, 'hex').toString());
            }
            catch (error) {
                throw new Error('Invalid token: Payload is not valid JSON');
            }
            // Extract the payload, permissions, expiration time, token type, and client_id
            const { _expiry, permissions, tokenType, client_id, ...payload } = payloadWithExpiry;
            const expirationTime = _expiry;
            // Check if the token has expired
            if (Date.now() > expirationTime) {
                throw new Error('Token has expired');
            }
            // Return the decoded token data, including client_id
            return {
                userId: parseInt(userId, 10),
                timestamp: payload.timestamp,
                payload,
                permissions: permissions || [],
                expirationTime,
                tokenType,
                client_id, // Ensure client_id is included in the return object
            };
        }
        catch (error) {
            console.error('Token validation failed:', error.message);
            return null;
        }
    }
    // Generate a client ID and secret with prefixes
    generateClientIdAndSecret() {
        const clientId = `Cl_${crypto.randomBytes(64).toString('hex')}`;
        const clientSecret = `Cs_${crypto.randomBytes(64).toString('hex')}`;
        return { clientId, clientSecret };
    }
    // Generate an access token using the grant format
    generateAccessToken(grant_type, client_id, client_secret, grantToken) {
        // Validate grant_type
        if (grant_type !== "authorization_code") {
            throw new Error('Invalid grant_type. Expected "authorization_code".');
        }
        // Validate client_id and client_secret
        if (!client_id || !client_secret) {
            throw new Error('client_id and client_secret are required.');
        }
        // Validate the grant token (code)
        const decodedToken = this.validateGrantToken(grantToken);
        if (!decodedToken) {
            throw new Error('Invalid or expired grant token.');
        }
        // Generate an access token (expires in 1 hour)
        const accessToken = this.generateGrantToken({ userId: decodedToken.userId, client_id }, 3600, // 1 hour in seconds
        decodedToken.permissions, // Pass permissions to the access token
        this.INTERNAL_CALL_KEY, // Internal call identifier
        'access' // Token type is 'access'
        );
        // Generate a refresh token (expires in 1 trillion years)
        const refreshToken = this.generateGrantToken({ userId: decodedToken.userId, client_id }, this.ONE_TRILLION_YEARS_IN_MS / 1000, // 1 trillion years in seconds
        decodedToken.permissions, // Pass permissions to the refresh token
        this.INTERNAL_CALL_KEY, // Internal call identifier
        'refresh' // Token type is 'refresh'
        );
        // Return the access token response
        return {
            access_token: accessToken,
            token_type: "Bearer",
            expires_in: 3600, // 1 hour in seconds
            refresh_token: refreshToken,
        };
    }
    // Refresh the access token using the refresh token
    refreshAccessToken(refresh_token, client_id, client_secret, grant_type) {
        // Validate grant_type
        if (grant_type !== "refresh_token") {
            throw new Error('Invalid grant_type. Expected "refresh_token".');
        }
        // Validate client_id and client_secret
        if (!client_id || !client_secret) {
            throw new Error('client_id and client_secret are required.');
        }
        // Validate the refresh token
        const decodedToken = this.validateGrantToken(refresh_token);
        if (!decodedToken) {
            throw new Error('Invalid or expired refresh token.');
        }
        // Ensure the token type is 'refresh'
        if (decodedToken.tokenType !== 'refresh') {
            throw new Error('Invalid token type. Expected a refresh token.');
        }
        // Check if the client_id matches
        if (decodedToken.payload.client_id !== client_id) {
            throw new Error('Invalid refresh token.');
        }
        // Generate a new access token (expires in 1 hour)
        const accessToken = this.generateGrantToken({ userId: decodedToken.userId, client_id }, 3600, // 1 hour in seconds
        decodedToken.permissions, // Pass permissions to the access token
        this.INTERNAL_CALL_KEY, // Internal call identifier
        'access' // Token type is 'access'
        );
        // Return the access token response
        return {
            access_token: accessToken,
            token_type: "Bearer",
            expires_in: 3600, // 1 hour in seconds
            refresh_token: refresh_token, // Return the same refresh token
        };
    }
}
exports.default = TokenService;
