import mongoose, { Schema, Document, ObjectId, Types } from 'mongoose';

const AddressSchema = new Schema({
  line1: { type: String }, 
  line2: { type: String }, 
  city: { type: String }, 
  state: { type: String, required: true }, 
  postal_code: { type: String, required: true }, 
  country: { type: String, required: true }, 
});

  export type KycDocumentType = 
    | 'government_id' 
    | 'passport'
    | 'drivers_license'
    | 'national_id'
    | 'business_registration'
    | 'proof_of_business'
    | 'tax_document';

  export type KycCategory = 'personal' | 'business';

const KycDocumentSchema = new Schema({
  type: { 
    type: String, 
    required: true,
    enum: [
      'government_id',
      'passport',
      'drivers_license',
      'national_id',
      'business_registration',
      'proof_of_business',
      'tax_document'
    ]
  },
  category: {
    type: String,
    required: true,
    enum: ['personal', 'business']
  },
  url: { type: String, required: true },
  verified: { type: Boolean, default: false },
  uploadedAt: { type: Date, default: Date.now },
  verifiedAt: { type: Date },
  status: { 
    type: String, 
    enum: ['pending', 'approved', 'rejected', 'not_submitted'], 
    default: 'not_submitted' 
  },
  notes: { type: String },
});

const UserSchema = new Schema({
  // Common fields for both Client and Business
  accountType: { type: String, enum: ['client', 'business'], required: true }, 
  full_legal_name: { type: String },
  first_name: { type: String }, 
  last_name: { type: String }, 
  date_of_birth: { type: Date },
  home_address: { type: AddressSchema }, 
  phone_number: { type: String },
  social_security: { type: String },
  country_of_residence: { type: String },
  last_login: { type: Date, default: null },
  payoutAccounts: [{
    type: Schema.Types.ObjectId,
    ref: 'Account.payoutAccounts'
  }],

  // Fields specific to Business
  legal_business_name: { type: String }, // Only for Business
  business_address: { type: AddressSchema }, // Use the AddressSchema for business address
  type_of_business_entity: { type: String }, // Only for Business
  tax_identification_number: { type: String }, // Only for Business
  country_of_business_residence: { type: String }, // Only for Business
  bank_account_details: { type: String }, // Only for Business
  estimated_transaction_volume: { type: String }, // Only for Business

  // KYC Documents
  documents: [KycDocumentSchema],

  // Authentication and verification fields
  email: { type: String },
  verificationCode: { type: String },
  verificationCodeExpiry: { type: Date },
  password: { type: String },
  kyc_status: { type: String, default: 'pending' },
  userIsVerified: { type: Boolean, default: false },
  role: { type: String, default: 'user' },
  permissions: { type: [String], default: [] },
  client_id: { type: String },
  client_secret: { type: String },
  redirect_uri: { type: String },
  loginPublicKey: { type: String },
  nonceExpiry: { type: Date, default: null },


  stripeAccountId: { type: String, default: null }, // Stripe account ID
  externalStripeAccountId: { type: String, default: null }, // External account ID

  profilePicture: { 
    url: { type: String, default: null },
    updatedAt: { type: Date }
  },
  kycDocuments: [{
    type: { type: String, required: true }, // e.g., 'passport', 'drivers_license', 'national_id'
    url: { type: String, required: true },
    verified: { type: Boolean, default: false },
    uploadedAt: { type: Date, default: Date.now },
    verifiedAt: { type: Date },
    status: { type: String, enum: ['pending', 'approved', 'rejected'], default: 'pending' }
  }],

  kyc: {
    personal: {
      government_id: [KycDocumentSchema],
      passport: [KycDocumentSchema],
      drivers_license: [KycDocumentSchema],
      national_id: [KycDocumentSchema],
      status: { 
        type: String, 
        enum: ['not_submitted', 'pending', 'verified'], 
        default: 'not_submitted' 
      }
    },
    business: {
      business_registration: [KycDocumentSchema],
      proof_of_business: [KycDocumentSchema],
      tax_document: [KycDocumentSchema],
      status: { 
        type: String, 
        enum: ['not_submitted', 'pending', 'verified'], 
        default: 'not_submitted' 
      }
    }
  }
});

// Address Interface
export interface IAddress {
  line1: string; // Required
  line2?: string; // Optional
  city: string; // Required
  state?: string; // Optional
  postal_code: string; // Required
  country: string; // Required
}

export interface IKycDocument {
  type: KycDocumentType;
  category: KycCategory;
  url: string;
  verified: boolean;
  uploadedAt: Date;
  verifiedAt?: Date;
  status: 'pending' | 'approved' | 'rejected' | 'not_submitted';
  notes?: string;
}

export interface IKycSection {
  status: 'not_submitted' | 'pending' | 'verified';
  [key: string]: IKycDocument[] | 'not_submitted' | 'pending' | 'verified';
}

// User Interface
export interface IUser extends Document {
  _id: ObjectId;
  accountType: 'client' | 'business';
  full_legal_name: string;
  first_name: string; 
  last_name: string; 
  date_of_birth: Date;
  home_address: IAddress; // Use the IAddress interface
  phone_number: string;
  social_security: string;
  country_of_residence: string;
  last_login: Date | null;
  payoutAccounts: Types.ObjectId[];


  // Business-specific fields (optional)
  legal_business_name?: string;
  business_address?: IAddress; // Use the IAddress interface for business address
  type_of_business_entity?: string;
  tax_identification_number?: string;
  country_of_business_residence?: string;
  bank_account_details?: string;
  estimated_transaction_volume?: string;
  average_transaction_size?: number;


  // KYC Documents
  documents: {
    type: string;
    url: string;
    verified: boolean;
  }[];

  // Authentication and verification fields
  email: string;
  verificationCode: string | null;
  verificationCodeExpiry: Date | null;
  password: string;
  kyc_status: string;
  userIsVerified: boolean;
  role: string;
  permissions: string[];
  client_id: string;
  client_secret: string;
  redirect_uri: string;
  loginPublicKey: string;
  nonceExpiry: Date | null; 

  stripeAccountId: string | null; // Stripe account ID
  externalStripeAccountId: string | null; // External account ID

  profilePicture?: {
    url: string | null;
    updatedAt?: Date;
  };
  kycDocuments: Array<{
    type: string;
    url: string;
    verified: boolean;
    uploadedAt: Date;
    verifiedAt?: Date;
    status: 'pending' | 'approved' | 'rejected';
  }>;

  kyc: {
    personal: IKycSection & {
      government_id: IKycDocument[];
      passport: IKycDocument[];
      drivers_license: IKycDocument[];
      national_id: IKycDocument[];
    };
    business: IKycSection & {
      business_registration: IKycDocument[];
      proof_of_business: IKycDocument[];
      tax_document: IKycDocument[];
    };
  };
}

// User Model
const User = mongoose.model<IUser>('User', UserSchema);

export default User;