"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserPayouts = exports.requestPayout = exports.setDefaultPayoutAccount = exports.getPayoutAccount = exports.getPayoutAccounts = exports.updatePayoutAccount = exports.addPayoutAccount = void 0;
const payout_model_1 = require("../models/payout.model");
const accountnumber_model_1 = require("../models/accountnumber.model");
const user_model_1 = __importDefault(require("../models/user.model"));
const mongoose_1 = __importDefault(require("mongoose"));
// Supported currencies and their configurations
const SUPPORTED_CURRENCIES = ['USD', 'LRD', 'SLL', 'GNF'];
// Add a payout account
const addPayoutAccount = async (req, res) => {
    const { userId, accountType, accountName, accountNumber, bankName, bankCode, currency, country } = req.body;
    try {
        // Validate input
        if (!userId || !accountType || !accountName || !accountNumber || !currency || !country) {
            return res.status(400).json({
                status: false,
                message: 'MISSING_REQUIRED_FIELDS',
                meta: {
                    error: 'Required fields are missing',
                    requiredFields: ['userId', 'accountType', 'accountName', 'accountNumber', 'currency', 'country']
                }
            });
        }
        // Validate currency
        const currencyUpper = currency.toUpperCase();
        if (!SUPPORTED_CURRENCIES.includes(currencyUpper)) {
            return res.status(400).json({
                status: false,
                message: 'UNSUPPORTED_CURRENCY',
                meta: {
                    error: `Currency ${currency} is not supported`,
                    supportedCurrencies: SUPPORTED_CURRENCIES
                }
            });
        }
        // Check if user exists
        const user = await user_model_1.default.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND'
            });
        }
        // Check if user already has a payout account for this currency
        const existingAccount = await payout_model_1.PayoutAccount.findOne({ userId, currency: currencyUpper });
        if (existingAccount) {
            return res.status(400).json({
                status: false,
                message: 'PAYOUT_ACCOUNT_EXISTS_FOR_CURRENCY',
                meta: {
                    error: `User already has a payout account for currency ${currencyUpper}`,
                    suggestion: 'Use the update endpoint to modify the existing account'
                }
            });
        }
        // Create new payout account - since it's the only one for this currency, set as default
        const payoutAccount = new payout_model_1.PayoutAccount({
            userId,
            accountType,
            accountName,
            accountNumber,
            bankName,
            bankCode,
            currency: currencyUpper,
            country,
            isDefault: true // Since each currency can have only one account, it's automatically default
        });
        await payoutAccount.save();
        // Update user's payoutAccounts array
        await user_model_1.default.findByIdAndUpdate(userId, {
            $push: { payoutAccounts: payoutAccount._id }
        });
        return res.status(201).json({
            status: true,
            message: 'PAYOUT_ACCOUNT_CREATED',
            data: payoutAccount
        });
    }
    catch (error) {
        console.error('Error creating payout account:', error);
        return res.status(500).json({
            status: false,
            message: 'INTERNAL_SERVER_ERROR',
            meta: {
                error: error.message
            }
        });
    }
};
exports.addPayoutAccount = addPayoutAccount;
// Update payout account
const updatePayoutAccount = async (req, res) => {
    const { accountId } = req.params;
    const { accountType, accountName, accountNumber, bankName, bankCode, country } = req.body;
    try {
        // Validate input
        if (!accountId) {
            return res.status(400).json({
                status: false,
                message: 'ACCOUNT_ID_REQUIRED'
            });
        }
        // Find and update the payout account
        const payoutAccount = await payout_model_1.PayoutAccount.findByIdAndUpdate(accountId, {
            accountType,
            accountName,
            accountNumber,
            bankName,
            bankCode,
            country,
            updatedAt: new Date()
        }, { new: true });
        if (!payoutAccount) {
            return res.status(404).json({
                status: false,
                message: 'PAYOUT_ACCOUNT_NOT_FOUND'
            });
        }
        return res.status(200).json({
            status: true,
            message: 'PAYOUT_ACCOUNT_UPDATED',
            data: payoutAccount
        });
    }
    catch (error) {
        console.error('Error updating payout account:', error);
        return res.status(500).json({
            status: false,
            message: 'INTERNAL_SERVER_ERROR',
            meta: {
                error: error.message
            }
        });
    }
};
exports.updatePayoutAccount = updatePayoutAccount;
// Get all payout accounts for a user
const getPayoutAccounts = async (req, res) => {
    const { userId } = req.params;
    try {
        if (!userId) {
            return res.status(400).json({
                status: false,
                message: 'USER_ID_REQUIRED'
            });
        }
        const payoutAccounts = await payout_model_1.PayoutAccount.find({ userId }).sort({ currency: 1 });
        if (!payoutAccounts || payoutAccounts.length === 0) {
            return res.status(404).json({
                status: false,
                message: 'NO_PAYOUT_ACCOUNTS_FOUND'
            });
        }
        return res.status(200).json({
            status: true,
            message: 'PAYOUT_ACCOUNTS_FOUND',
            data: payoutAccounts
        });
    }
    catch (error) {
        console.error('Error fetching payout accounts:', error);
        return res.status(500).json({
            status: false,
            message: 'INTERNAL_SERVER_ERROR',
            meta: {
                error: error.message
            }
        });
    }
};
exports.getPayoutAccounts = getPayoutAccounts;
// Get a specific payout account
const getPayoutAccount = async (req, res) => {
    const { accountId } = req.params;
    try {
        if (!accountId) {
            return res.status(400).json({
                status: false,
                message: 'ACCOUNT_ID_REQUIRED'
            });
        }
        const payoutAccount = await payout_model_1.PayoutAccount.findById(accountId);
        if (!payoutAccount) {
            return res.status(404).json({
                status: false,
                message: 'PAYOUT_ACCOUNT_NOT_FOUND'
            });
        }
        return res.status(200).json({
            status: true,
            message: 'PAYOUT_ACCOUNT_FOUND',
            data: payoutAccount
        });
    }
    catch (error) {
        console.error('Error fetching payout account:', error);
        return res.status(500).json({
            status: false,
            message: 'INTERNAL_SERVER_ERROR',
            meta: {
                error: error.message
            }
        });
    }
};
exports.getPayoutAccount = getPayoutAccount;
// Set default payout account
const setDefaultPayoutAccount = async (req, res) => {
    const { accountId } = req.params;
    const { userId } = req.body;
    try {
        if (!accountId || !userId) {
            return res.status(400).json({
                status: false,
                message: 'ACCOUNT_ID_AND_USER_ID_REQUIRED'
            });
        }
        // First reset all accounts for this user to non-default
        await payout_model_1.PayoutAccount.updateMany({ userId }, { $set: { isDefault: false } });
        // Then set the specified account as default
        const payoutAccount = await payout_model_1.PayoutAccount.findByIdAndUpdate(accountId, { $set: { isDefault: true } }, { new: true });
        if (!payoutAccount) {
            return res.status(404).json({
                status: false,
                message: 'PAYOUT_ACCOUNT_NOT_FOUND'
            });
        }
        return res.status(200).json({
            status: true,
            message: 'DEFAULT_PAYOUT_ACCOUNT_SET',
            data: payoutAccount
        });
    }
    catch (error) {
        console.error('Error setting default payout account:', error);
        return res.status(500).json({
            status: false,
            message: 'INTERNAL_SERVER_ERROR',
            meta: {
                error: error.message
            }
        });
    }
};
exports.setDefaultPayoutAccount = setDefaultPayoutAccount;
// Request payout
const requestPayout = async (req, res) => {
    const { userId, amount, currency, note } = req.body;
    const requestTimestamp = new Date();
    try {
        // Validate input
        if (!userId || !amount || amount <= 0 || !currency) {
            return res.status(400).json({
                status: false,
                message: 'INVALID_INPUT',
                timestamp: requestTimestamp.toISOString(),
                meta: {
                    error: 'Missing required fields or invalid amount',
                    requiredFields: ['userId', 'amount (>0)', 'currency'],
                    optionalFields: ['note']
                }
            });
        }
        const currencyUpper = currency.toUpperCase();
        if (!SUPPORTED_CURRENCIES.includes(currencyUpper)) {
            return res.status(400).json({
                status: false,
                message: 'UNSUPPORTED_CURRENCY',
                timestamp: requestTimestamp.toISOString(),
                meta: {
                    error: `Currency ${currency} is not supported`,
                    supportedCurrencies: SUPPORTED_CURRENCIES
                }
            });
        }
        // Check user and account
        const user = await user_model_1.default.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                timestamp: requestTimestamp.toISOString()
            });
        }
        const account = await accountnumber_model_1.Account.findOne({ userId });
        if (!account) {
            return res.status(404).json({
                status: false,
                message: 'ACCOUNT_NOT_FOUND',
                timestamp: requestTimestamp.toISOString()
            });
        }
        // Check user's payout account for this currency
        const payoutAccount = await payout_model_1.PayoutAccount.findOne({ userId, currency: currencyUpper });
        if (!payoutAccount) {
            return res.status(400).json({
                status: false,
                message: 'PAYOUT_ACCOUNT_NOT_SETUP_FOR_CURRENCY',
                timestamp: requestTimestamp.toISOString(),
                meta: {
                    error: `User has not set up a payout account for currency ${currencyUpper}`,
                    suggestion: 'Create a payout account for this currency first'
                }
            });
        }
        // Check wallet balance (but don't deduct yet)
        const userBalance = account.balance.find((b) => b.currency === currencyUpper);
        if (!userBalance) {
            return res.status(400).json({
                status: false,
                message: 'CURRENCY_WALLET_NOT_FOUND',
                timestamp: requestTimestamp.toISOString(),
                meta: { error: `No wallet found for currency '${currencyUpper}'` }
            });
        }
        const currentWalletBalance = Number(userBalance.walletBalance);
        const payoutAmount = Number(amount);
        if (currentWalletBalance < payoutAmount) {
            return res.status(400).json({
                status: false,
                message: 'INSUFFICIENT_FUNDS',
                timestamp: requestTimestamp.toISOString(),
                meta: {
                    error: `Insufficient balance in your ${currencyUpper} wallet`,
                    currentBalance: currentWalletBalance,
                    requestedAmount: payoutAmount
                }
            });
        }
        // Free withdrawal for now
        const netAmount = payoutAmount;
        const fee = 0;
        const reference = `SANGAPAY-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
        const createdAt = new Date();
        // Create payout request in pending state (no deduction)
        const payout = new payout_model_1.Payout({
            userId,
            payoutAccountId: payoutAccount._id,
            reference: reference,
            amount: payoutAmount,
            fee,
            netAmount,
            currency: currencyUpper,
            note,
            status: 'pending',
            initiatedBy: user._id,
            destination: {
                accountName: payoutAccount.accountName,
                accountNumber: payoutAccount.accountNumber,
                bankName: payoutAccount.bankName,
                country: payoutAccount.country,
                accountType: payoutAccount.accountType
            },
            statusHistory: [{
                    status: 'pending',
                    changedAt: createdAt,
                    changedBy: user._id,
                    reason: 'Payout request submitted'
                }],
            processLogs: [{
                    step: 'requested',
                    details: `Payout request of ${payoutAmount} ${currencyUpper} submitted by user. ${note ? `Note: ${note}` : ''}` +
                        `\nFee: ${fee} ${currencyUpper}, Net Amount: ${netAmount} ${currencyUpper}` +
                        '\nFunds not deducted - pending approval',
                    timestamp: createdAt
                }],
            createdAt,
            updatedAt: createdAt
        });
        await payout.save();
        return res.status(200).json({
            status: true,
            message: 'PAYOUT_REQUEST_SUBMITTED_PENDING_APPROVAL',
            timestamp: requestTimestamp.toISOString(),
            data: {
                payoutId: payout._id,
                amount: payoutAmount,
                reference: reference,
                fee: fee,
                netAmount: netAmount,
                currency: currencyUpper,
                note: note,
                status: 'pending',
                currentWalletBalance,
                requiredApproval: true,
                timestamps: {
                    requestedAt: createdAt.toISOString(),
                    updatedAt: createdAt.toISOString()
                },
                payoutDetails: {
                    accountName: payoutAccount.accountName,
                    accountNumber: payoutAccount.accountNumber,
                    bankName: payoutAccount.bankName
                }
            }
        });
    }
    catch (error) {
        console.error('Payout request failed:', error);
        const errorTimestamp = new Date();
        return res.status(500).json({
            status: false,
            message: 'PAYOUT_REQUEST_FAILED',
            timestamp: errorTimestamp.toISOString(),
            meta: {
                error: error.message,
                suggestions: [
                    'Verify account details',
                    'Check your balance',
                    'Contact support if issue persists'
                ]
            }
        });
    }
};
exports.requestPayout = requestPayout;
const getUserPayouts = async (req, res) => {
    const { userId } = req.params;
    try {
        // Validate userId
        if (!userId || !mongoose_1.default.Types.ObjectId.isValid(userId)) {
            return res.status(400).json({
                error: 'INVALID_USER_ID',
                message: 'The provided userId is missing or not valid'
            });
        }
        // Fetch all payouts for the user
        const payouts = await payout_model_1.Payout.find({ userId })
            .sort({ createdAt: -1 })
            .populate('payoutAccountId', 'accountName accountNumber bankName')
            .lean();
        // Format response data
        const formattedPayouts = payouts.map(payout => ({
            payoutId: payout._id,
            reference: payout.reference,
            amount: payout.amount,
            fee: payout.fee,
            netAmount: payout.netAmount,
            currency: payout.currency,
            status: payout.status,
            note: payout.note,
            destination: {
                accountName: payout.destination.accountName,
                accountNumber: payout.destination.accountNumber,
                bankName: payout.destination.bankName
            },
            timestamps: {
                createdAt: payout.createdAt.toISOString(),
                updatedAt: payout.updatedAt.toISOString(),
                completedAt: payout.completedAt?.toISOString()
            },
            statusHistory: payout.statusHistory.map(history => ({
                status: history.status,
                changedAt: history.changedAt.toISOString(),
                reason: history.reason
            }))
        }));
        return res.status(200).json(formattedPayouts);
    }
    catch (error) {
        console.error('Error fetching payouts:', error);
        return res.status(500).json({
            error: 'PAYOUTS_RETRIEVAL_FAILED',
            message: error.message
        });
    }
};
exports.getUserPayouts = getUserPayouts;
