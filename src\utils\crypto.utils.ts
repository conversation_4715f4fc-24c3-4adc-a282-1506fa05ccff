import * as crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

const algorithm = 'aes-256-cbc';
const SECRET_KEY = process.env.SECRET_KEY; // Load the SECRET_KEY from .env
const IV_LENGTH = 16; // AES block size is 16 bytes

if (!SECRET_KEY) {
  throw new Error('CRYPTO_SECRET_KEY is not defined in the environment variables.');
}

// Convert the SECRET_KEY from hex to a buffer
const keyBuffer = Buffer.from(SECRET_KEY, 'hex');

// Debug: Log the key buffer and its length
console.log('Key Buffer:', keyBuffer);
console.log('Key Buffer Length:', keyBuffer.length);

// Ensure the SECRET_KEY is exactly 32 bytes long
if (keyBuffer.length !== 32) {
  throw new Error('SECRET_KEY must be exactly 32 bytes long for AES-256 encryption.');
}

export const encrypt = (text: string): string => {
  const iv = crypto.randomBytes(IV_LENGTH); // Generate a random initialization vector
  const cipher = crypto.createCipheriv(algorithm, keyBuffer, iv);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return `${iv.toString('hex')}:${encrypted}`; // Return IV and encrypted data
};

export const decrypt = (text: string): string => {
  const [ivHex, encryptedText] = text.split(':');
  if (!ivHex || !encryptedText) {
    throw new Error('Invalid encrypted text format.');
  }
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = crypto.createDecipheriv(algorithm, keyBuffer, iv);
  let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};