"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Transaction = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const TransactionSchema = new mongoose_1.Schema({
    transactionId: { type: String, required: true },
    type: {
        type: String,
        required: true,
        enum: ['DEBIT', 'CREDIT', 'TRANSFER', 'SWAP', 'WITHDRAW', 'DEPOSIT', 'CARD'],
    },
    title: { type: String, required: true },
    amount: { type: Number, required: true },
    sender: {
        userId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User', required: true },
        name: { type: String, required: true },
        accountNumber: { type: String, required: true },
        username: { type: String },
    },
    recipient: {
        userId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User' },
        name: { type: String },
        accountNumber: { type: String },
        username: { type: String },
        email: { type: String },
    },
    customDescription: { type: String },
    predefinedDescription: { type: String, required: true },
    status: {
        type: String,
        required: true,
        enum: ['PENDING', 'SUCCESS', 'FAILED'],
        default: 'PENDING',
    },
    timestamp: { type: Date, default: Date.now },
    cardTransaction: {
        cardId: { type: String },
        cardType: { type: String },
    },
    receipt: { type: String },
});
// Custom validation to make recipient optional for CARD transactions
TransactionSchema.pre('validate', function (next) {
    if (this.type !== 'CARD' && !this.recipient) {
        this.invalidate('recipient', 'Recipient is required');
    }
    next();
});
exports.Transaction = mongoose_1.default.model('Transaction', TransactionSchema);
