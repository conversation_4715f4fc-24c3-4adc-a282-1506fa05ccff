import express from 'express';
import { decodeToken, generateAccessToken, generateGrantToken, getClientCredentials, refreshAccessTokenController } from '../controllers/developer.controller';
import { authenticateToken } from '../middleware/auth.middleware';

const devRouter = express.Router();

devRouter.post('/authorization', generateGrantToken); 
devRouter.post('/decode', decodeToken);
devRouter.post('/access-token', generateAccessToken);
devRouter.post('/refresh-token', refreshAccessTokenController);
devRouter.get('/user/:userId/credentials', authenticateToken, getClientCredentials);




export default devRouter;
