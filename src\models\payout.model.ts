import mongoose, { Schema, Document } from 'mongoose';

// Supported currencies configuration
const SUPPORTED_CURRENCIES = ['USD', 'LRD', 'SLL', 'GNF'];

// Payout Account Schema
const PayoutAccountSchema = new Schema({
  userId: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true,
    index: true 
  },
  accountType: { 
    type: String, 
    required: true, 
    enum: ['bank', 'savings', 'current', 'mobile_money'] 
  },
  accountName: { 
    type: String, 
    required: true,
    trim: true
  },
  accountNumber: { 
    type: String, 
    required: true,
    trim: true,
    index: true
  },
  bankName: { 
    type: String,
    trim: true
  },
  bankCode: { 
    type: String,
    trim: true
  },
  currency: { 
    type: String, 
    required: true,
    enum: SUPPORTED_CURRENCIES,
    index: true
  },
  country: { 
    type: String, 
    required: true,
    trim: true
  },
  isDefault: { 
    type: Boolean, 
    default: false,
    index: true
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  verificationDetails: {
    verifiedAt: Date,
    verifiedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    method: String
  },
  metadata: Schema.Types.Mixed,
  createdAt: { 
    type: Date, 
    default: Date.now,
    index: true 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Add compound index to ensure one payout account per currency per user
PayoutAccountSchema.index(
  { userId: 1, currency: 1 }, 
  { unique: true }
);

// Payout Status Types
const PAYOUT_STATUSES = [
  'pending',       // Initial state
  'approved',      // Approved by admin
  'processing',    // Sent to payment processor
  'completed',     // Successfully processed
  'failed',        // Failed at any stage
  'cancelled'      // Cancelled by user or admin
] as const;

// Payout Schema
const PayoutSchema = new Schema({
  userId: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true,
    index: true 
  },
  payoutAccountId: { 
    type: Schema.Types.ObjectId, 
    ref: 'PayoutAccount', 
    required: true,
    index: true 
  },
  reference: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  amount: { 
    type: Number, 
    required: true,
    min: 0.01
  },
  fee: {
    type: Number,
    default: 0
  },
  netAmount: {
    type: Number,
    required: true
  },
  currency: { 
    type: String, 
    required: true, 
    enum: SUPPORTED_CURRENCIES,
    index: true
  },
  note: { 
    type: String,
    trim: true
  }, 
  status: {
    type: String,
    required: true,
    enum: PAYOUT_STATUSES,
    default: 'pending',
    index: true
  },
  statusHistory: [
    {
      status: {
        type: String,
        enum: PAYOUT_STATUSES,
        required: true
      },
      changedAt: {
        type: Date,
        default: Date.now
      },
      changedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      },
      reason: String
    }
  ],
  destination: {
    accountType: String,
    accountName: String,
    accountNumber: String,
    bankName: String,
    bankCode: String,
    country: String
  },
  processorResponse: Schema.Types.Mixed,
  metadata: Schema.Types.Mixed,
  initiatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  approvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  completedAt: Date,
  createdAt: { 
    type: Date, 
    default: Date.now,
    index: true 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Auto-generate reference before save
PayoutSchema.pre('save', function(next) {
  if (!this.reference) {
    // Generate a unique reference: PAY-{timestamp}-{random}
    this.reference = `PAY-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  }
  // Calculate net amount
  this.netAmount = this.amount - this.fee;
  next();
});

// Payout Account Interface
export interface IPayoutAccount extends Document {
  userId: mongoose.Types.ObjectId;
  accountType: string;
  accountName: string;
  accountNumber: string;
  bankName?: string;
  bankCode?: string;
  currency: string;
  country: string;
  isDefault: boolean;
  isVerified: boolean;
  verificationDetails?: {
    verifiedAt: Date;
    verifiedBy?: mongoose.Types.ObjectId;
    method?: string;
  };
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

// Payout Status Type
export type PayoutStatus = typeof PAYOUT_STATUSES[number];

// Payout Interface
export interface IPayout extends Document {
  userId: mongoose.Types.ObjectId;
  payoutAccountId: mongoose.Types.ObjectId;
  reference: string;
  amount: number;
  fee: number;
  netAmount: number;
  currency: string;
  note?: string;
  status: PayoutStatus;
  statusHistory: {
    status: PayoutStatus;
    changedAt: Date;
    changedBy?: mongoose.Types.ObjectId;
    reason?: string;
  }[];
  destination: {
    accountType?: string;
    accountName?: string;
    accountNumber?: string;
    bankName?: string;
    bankCode?: string;
    country?: string;
  };
  processorResponse?: any;
  metadata?: any;
  initiatedBy: mongoose.Types.ObjectId;
  approvedBy?: mongoose.Types.ObjectId;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Create and export the models
const PayoutAccount = mongoose.model<IPayoutAccount>('PayoutAccount', PayoutAccountSchema);
const Payout = mongoose.model<IPayout>('Payout', PayoutSchema);

export { Payout, PayoutAccount, PAYOUT_STATUSES, SUPPORTED_CURRENCIES };