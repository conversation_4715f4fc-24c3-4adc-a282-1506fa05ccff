"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTransactionReceipt = exports.generateTransactionId = exports.createAccountService = void 0;
exports.getAccountNumberByUserId = getAccountNumberByUserId;
const accountnumber_model_1 = require("../models/accountnumber.model");
const async_mutex_1 = require("async-mutex");
const crypto_1 = __importDefault(require("crypto"));
const mutex = new async_mutex_1.Mutex(); // Create a mutex instance
let currentLength = 10; // Start with 10-digit account numbers
// Generate an account number with dynamic length
const generateAccountNumber = (starter, accountID) => {
    const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14); // YYYYMMDDHHMMSS
    const initialValue = `${starter}${accountID}`;
    // Calculate padding length based on current account number length
    const paddingLength = currentLength - 1 - accountID.toString().length - 2;
    const paddedValue = initialValue + timestamp.slice(0, paddingLength);
    // Add a random 2-digit suffix
    const randomSuffix = Math.floor(10 + Math.random() * 90).toString();
    return `${paddedValue}${randomSuffix}`;
};
// Get the next value from a sequence
const getNextSequenceValue = async (sequenceName) => {
    const sequence = await accountnumber_model_1.Sequence.findOneAndUpdate({ name: sequenceName }, { $inc: { value: 1 } }, { new: true, upsert: true });
    if (!sequence) {
        throw new Error('Failed to generate sequence value');
    }
    return sequence.value;
};
// Create an account
const createAccountService = async (userId, account_type) => {
    const release = await mutex.acquire(); // Acquire the mutex lock
    try {
        // Get the next accountID from the sequence
        const sequenceName = `accountID_${currentLength}`;
        let accountID = await getNextSequenceValue(sequenceName);
        // Check if the current length limit is reached
        const maxAccountID = Math.pow(10, currentLength - 3) - 1; // Max AccountID for current length
        if (accountID > maxAccountID) {
            // Expand the account number length
            currentLength += 1;
            accountID = 1; // Reset AccountID for the new length
        }
        // Generate the account number
        const starter = '1'; // Fixed starter for simplicity
        const accountNumber = generateAccountNumber(starter, accountID);
        // Initialize balance for all supported currencies (from your schema)
        const balance = [
            { currency: 'USD', amount: 0 },
            { currency: 'LRD', amount: 0 },
            { currency: 'SLL', amount: 0 },
            { currency: 'GNF', amount: 0 },
        ];
        // Save the account to the database
        const account = new accountnumber_model_1.Account({
            accountNumber,
            userId,
            account_type,
            balance, // Store balances in the database as per your schema
            countries: ['USD', 'LRD', 'SLL', 'GNF'], // Default supported countries/currencies
            status: 'active', // Default status
        });
        await account.save();
        return {
            id: account._id,
            account_number: account.accountNumber,
            account_type: account.account_type,
            balance: account.balance, // Ensure correct balance is returned
            countries: account.countries, // Return the supported countries/currencies
            status: account.status,
            created_at: account.created_at,
        };
    }
    catch (error) {
        console.error('Error during account creation:', error);
        throw error;
    }
    finally {
        release(); // Release the mutex lock
    }
};
exports.createAccountService = createAccountService;
async function getAccountNumberByUserId(userId) {
    try {
        // Query the Account collection for the user's account
        const account = await accountnumber_model_1.Account.findOne({ userId: userId }).exec();
        if (!account) {
            console.error('Account not found for user ID:', userId);
            return null;
        }
        // Return the account number
        return account.accountNumber;
    }
    catch (error) {
        console.error('Error fetching account:', error);
        throw error;
    }
}
// Helper function to generate SHA-256 hashes
const generateTransactionId = (input) => {
    const hash = crypto_1.default.createHash('sha256').update(input).digest('hex');
    return hash.slice(0, 32).toUpperCase(); // Convert to uppercase for consistency
};
exports.generateTransactionId = generateTransactionId;
// Helper function to create a transaction receipt
const createTransactionReceipt = (prefix, transactionType, timestamp, transactionId) => {
    return `${prefix}|${transactionType}|${timestamp}|${transactionId}`;
};
exports.createTransactionReceipt = createTransactionReceipt;
