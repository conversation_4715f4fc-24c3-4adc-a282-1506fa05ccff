import { Request, Response } from 'express';
import { CurrencyConversionService } from '../services/exchange.service';
import { Account } from '../models/accountnumber.model';
import User from '../models/user.model';

const currencyConversionService = new CurrencyConversionService();

const HARDCODED_SYMBOLS = 'NGN,SLL,LRD,GNF';

export const getLatestRates = async (req: Request, res: Response): Promise<any> => {
  const { userId, includeAllRates } = req.query;

  if (!userId) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_REQUIRED_FIELDS',
      meta: {
        error: 'userId is required.',
        suggestions: ['Provide a userId in the query parameters.'],
      },
    });
  }

  try {
    const latestRates = await currencyConversionService.getLatestRatesFromUSD(
      HARDCODED_SYMBOLS,
      includeAllRates === 'true' 
    );

    if (!latestRates.success) {
      throw new Error('Failed to fetch latest rates');
    }

    const usdToAllRates = latestRates.importantRates;
    const allToUsdRates: { [key: string]: { rate: number; type: string } } = {};
    for (const currency in usdToAllRates) {
      allToUsdRates[currency] = {
        rate: 1 / usdToAllRates[currency], // Inverse of USD to currency
        type: `${currency}TOUSD`,
      };
    }

    // Calculate intra-currency rates (e.g., NGN to SLL, SLL to GNF, etc.)
    const intraCurrencyRates: { [key: string]: { [key: string]: { rate: number; type: string } } } = {};
    const currencies = HARDCODED_SYMBOLS.split(',');

    for (const baseCurrency of currencies) {
      intraCurrencyRates[baseCurrency] = {};
      for (const targetCurrency of currencies) {
        if (baseCurrency === targetCurrency) {
          intraCurrencyRates[baseCurrency][targetCurrency] = {
            rate: 1, // Same currency
            type: `${baseCurrency}TO${targetCurrency}`,
          };
        } else {
          // Calculate rate using USD as the base
          intraCurrencyRates[baseCurrency][targetCurrency] = {
            rate: usdToAllRates[targetCurrency] / usdToAllRates[baseCurrency],
            type: `${baseCurrency}TO${targetCurrency}`,
          };
        }
      }
    }

    const response = {
      status: true,
      message: 'LATEST_RATES_FETCHED_SUCCESSFULLY',
      userId,
      data: {
        usdToAllRates: Object.keys(usdToAllRates).reduce((acc, currency) => {
          acc[currency] = {
            rate: usdToAllRates[currency],
            type: `USDTO${currency}`,
          };
          return acc;
        }, {} as { [key: string]: { rate: number; type: string } }),
        allToUsdRates,
        intraCurrencyRates,
        allRates: latestRates.allRates, 
      },
    };

    return res.status(200).json(response);
  } catch (error: any) {
    console.error('Error fetching latest rates:', error);

    return res.status(500).json({
      status: false,
      message: 'FAILED_TO_FETCH_LATEST_RATES',
      meta: {
        error: error.message,
        suggestions: ['Check your input data for errors.', 'Try again later.'],
      },
    });
  }
};


/**
 * API endpoint to swap balances.
 */
export const swap = async (req: Request, res: Response): Promise<any> => {
  const { userId, sourceCurrency, targetCurrency, amount } = req.body;

  // Input validation
  if (!userId || !sourceCurrency || !targetCurrency || !amount) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_REQUIRED_FIELDS',
      meta: {
        error: 'userId, sourceCurrency, targetCurrency, and amount are required.',
        suggestions: ['Provide all required fields in the request body.'],
      },
      timestamp: new Date().toISOString(), // Add timestamp
    });
  }

  try {
    // Fetch the user's details
    const user = await User.findById(userId).exec();
    if (!user) {
      return res.status(404).json({
        status: false,
        message: 'USER_NOT_FOUND',
        meta: {
          error: 'User not found for the provided userId.',
          suggestions: ['Check the userId and try again.'],
        },
        timestamp: new Date().toISOString(), // Add timestamp
      });
    }

    // Fetch the user's account
    const account = await Account.findOne({ userId }).exec();
    if (!account) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'Account not found for the provided userId.',
          suggestions: ['Check the userId and try again.'],
        },
        timestamp: new Date().toISOString(), // Add timestamp
      });
    }

    // Ensure the source and target currencies belong to the user's account
    const sourceBalance = account.balance.find((b) => b.currency === sourceCurrency);
    const targetBalance = account.balance.find((b) => b.currency === targetCurrency);

    if (!sourceBalance) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_SOURCE_CURRENCY',
        meta: {
          error: 'The source currency does not belong to your account.',
          suggestions: ['Check the sourceCurrency and try again.'],
        },
        timestamp: new Date().toISOString(), // Add timestamp
      });
    }

    if (!targetBalance) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_TARGET_CURRENCY',
        meta: {
          error: 'The target currency does not belong to your account.',
          suggestions: ['Check the targetCurrency and try again.'],
        },
        timestamp: new Date().toISOString(), // Add timestamp
      });
    }

    // Ensure the source and target currencies are different
    if (sourceCurrency === targetCurrency) {
      return res.status(400).json({
        status: false,
        message: 'SAME_CURRENCY_SWAP',
        meta: {
          error: 'Source and target currencies cannot be the same.',
          suggestions: ['Choose different currencies for the swap.'],
        },
        timestamp: new Date().toISOString(), // Add timestamp
      });
    }

    // Call the swapBalances service
    const swapResult = await currencyConversionService.swapBalances(
      account, // Pass the account to the service
      user, // Pass the user details to the service
      sourceCurrency,
      targetCurrency,
      amount
    );

    if (!swapResult.success) {
      return res.status(400).json({
        status: false,
        message: swapResult.message,
        meta: {
          error: swapResult.message,
          suggestions: ['Check your input data for errors.', 'Try again later.'],
        },
        timestamp: new Date().toISOString(), // Add timestamp
      });
    }

    return res.status(200).json({
      status: true,
      message: swapResult.message,
      data: swapResult.data,
      timestamp: new Date().toISOString(), // Add timestamp
    });
  } catch (error: any) {
    console.error('Error swapping balances:', error);

    return res.status(500).json({
      status: false,
      message: 'FAILED_TO_SWAP_BALANCES',
      meta: {
        error: error.message,
        suggestions: ['Check your input data for errors.', 'Try again later.'],
      },
      timestamp: new Date().toISOString(), // Add timestamp
    });
  }
};