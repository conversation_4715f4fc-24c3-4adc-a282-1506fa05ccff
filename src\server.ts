import dotenv from 'dotenv';
import express from 'express';
import mongoose from 'mongoose';
import http from 'http';
import cors from 'cors';
import helmet from 'helmet';
import { createClient } from 'redis'; 
import authRouter from './routes/auth.route';
import userRouter from './routes/user.route';
import accountRouter from './routes/account.route';
import callbackRouter from './routes/callback.route';
import devRouter from './routes/dev.route';
import helperRouter from './routes/helper.route';
import adminRouter from './routes/admin.route';
import payoutRouter from './routes/payout.route';
import { findMalformedBalances, migrateBalancesToWallet } from './controllers/helper.controller';
import billpaymentRouter from './routes/billpayment.route';

dotenv.config();

const app = express();

app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

const port = process.env.PORT || 5000;
const mongoURI = process.env.MONGODB_URI;

const server = http.createServer(app);

// Redis client setup
// const redisClient = createClient({
//   url: process.env.REDIS_URL, // Use the REDIS_URL from environment variables
// });

// redisClient.on('error', (err: any) => {
//   console.error('Redis Client Error:', err);
// });

// (async () => {
//   await redisClient.connect();
//   console.log('Connected to Redis');
// })();

// Database connection
const connectToDatabase = async () => {
  try {
    await mongoose.connect(`${mongoURI}`);
    console.log('Connected to MongoDB');

  } catch (error) {
    console.error('Error connecting to MongoDB:', getErrorMessage(error));
    process.exit(1);
  }
};
connectToDatabase();

// Routes
app.use('/api/v1/auth', authRouter);
app.use('/api/v1/user', userRouter);
app.use('/api/v1/acc', accountRouter);
app.use('/api/v1/callback', callbackRouter);
app.use('/api/v1/oauth', devRouter);
app.use('/api/v1/helper', helperRouter);
app.use('/api/v1/enya', payoutRouter);
app.use('/api/v1/nebula', adminRouter);
app.use('/api/v1/billpayment', billpaymentRouter);




// Base route - Status check for API
app.get('/', (req, res) => {
  res.status(200).json({
    status: true,
    message: 'Ok.',
  });
});

app.get('/api/v1', (req, res) => {
  res.status(200).json({
    status: true,
    message: 'Ok.',
  });
});

// Start the server
server.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});

function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unknown error occurred';
}


