"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.linkCard = exports.createStripeAccount = exports.handleStripeWebhook = exports.createPaymentIntent = exports.retrieveAndDecryptCardDetails = exports.createCardholderIssueAndRetrieveCard = exports.retrieveCreditCard = exports.issueCreditCard = exports.createCardholder = void 0;
const stripe_service_1 = require("../services/stripe.service");
const user_model_1 = __importDefault(require("../models/user.model"));
const accountnumber_model_1 = require("../models/accountnumber.model");
const creditcardencrption_service_1 = __importDefault(require("../services/creditcardencrption.service"));
const transaction_model_1 = require("../models/transaction.model");
const accountnumber_service_1 = require("../services/accountnumber.service");
const stripe_1 = __importDefault(require("stripe"));
const stripeService = new stripe_service_1.StripeService();
const encryptionService = new creditcardencrption_service_1.default();
const createCardholder = async (req, res) => {
    const { userId } = req.body;
    // Input validation
    if (!userId) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'userId is required.',
                suggestions: ['Provide userId.'],
            },
        });
    }
    try {
        // Fetch the user and account details
        const user = await user_model_1.default.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided ID does not exist.',
                    suggestions: ['Check the userId and try again.'],
                },
            });
        }
        const account = await accountnumber_model_1.Account.findOne({ userId: user._id });
        if (!account) {
            return res.status(404).json({
                status: false,
                message: 'ACCOUNT_NOT_FOUND',
                meta: {
                    error: 'Account for the provided user does not exist.',
                    suggestions: ['Check the userId and try again.'],
                },
            });
        }
        // Determine the billing address based on the account type
        let billingAddress;
        if (user.accountType === 'business') {
            // Use business address for business accounts
            if (!user.business_address) {
                return res.status(400).json({
                    status: false,
                    message: 'BUSINESS_ADDRESS_REQUIRED',
                    meta: {
                        error: 'Business address is required for business accounts.',
                        suggestions: ['Update the user profile with a valid business address.'],
                    },
                });
            }
            billingAddress = user.business_address;
        }
        else {
            // Use home address for client accounts
            if (!user.home_address) {
                return res.status(400).json({
                    status: false,
                    message: 'HOME_ADDRESS_REQUIRED',
                    meta: {
                        error: 'Home address is required for client accounts.',
                        suggestions: ['Update the user profile with a valid home address.'],
                    },
                });
            }
            billingAddress = user.home_address;
        }
        // Split the full legal name into first_name and last_name
        const nameParts = user.full_legal_name.split(' ');
        const first_name = nameParts[0]; // First part is the first name
        const last_name = nameParts.slice(1).join(' '); // The rest is the last name
        // Extract date_of_birth and split into day, month, and year
        const date_of_birth = new Date(user.date_of_birth); // Assuming date_of_birth is stored in the user object
        const dob = {
            day: date_of_birth.getUTCDate(), // Day
            month: date_of_birth.getUTCMonth() + 1, // Month (0-indexed, so add 1)
            year: date_of_birth.getUTCFullYear(), // Year
        };
        // Capture user_terms_acceptance details from the request
        const user_terms_acceptance = {
            date: Math.floor(Date.now() / 1000), // Current Unix timestamp
            ip: req.ip, // IP address from the request
            user_agent: req.headers['user-agent'], // User agent from the request headers
        };
        // Create a cardholder in Stripe
        const cardholder = await stripeService.createCardholder({
            name: user.full_legal_name, // Use the user's name from the User model
            email: user.email,
            phone_number: user.phone_number,
            billing_address: {
                line1: billingAddress.line1,
                city: billingAddress.city,
                state: billingAddress.state || '', // Optional field
                postal_code: billingAddress.postal_code,
                country: billingAddress.country,
            },
            metadata: {
                accountNumber: account.accountNumber,
            },
            individual: {
                first_name, // First name from the split
                last_name, // Last name from the split
                dob, // Date of birth object
                card_issuing: {
                    user_terms_acceptance, // User terms acceptance details
                },
            },
        });
        // Save the cardholder data to the Account model
        account.creditCard = {
            cardholderId: cardholder.id,
            cardDetails: null,
            name: cardholder.name,
            email: cardholder.email,
            phone_number: cardholder.phone_number,
            billing_address: {
                city: cardholder.billing.address.city,
                country: cardholder.billing.address.country,
                line1: cardholder.billing.address.line1,
                line2: cardholder.billing.address.line2 || '', // Optional field
                postal_code: cardholder.billing.address.postal_code,
                state: cardholder.billing.address.state || '', // Optional field
            },
            metadata: cardholder.metadata, // Save all metadata (including accountNumber, userId, userType, etc.)
            status: cardholder.status,
            created: new Date(cardholder.created * 1000), // Convert Unix timestamp to Date
        };
        // Save the updated account document
        await account.save();
        return res.status(201).json({
            status: true,
            message: 'CARDHOLDER_CREATED_SUCCESSFULLY',
            data: cardholder,
        });
    }
    catch (error) {
        console.error('Error creating cardholder:', error);
        return res.status(500).json({
            status: false,
            message: 'CARDHOLDER_CREATION_FAILED',
            meta: {
                error: error.message,
                suggestions: ['Check your input data for errors.', 'Try again later.'],
            },
        });
    }
};
exports.createCardholder = createCardholder;
const issueCreditCard = async (req, res) => {
    const { userId, type } = req.body;
    // Input validation
    if (!userId || !type) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'userId and type are required.',
                suggestions: ['Provide userId and type.'],
            },
        });
    }
    try {
        // Fetch the user and account details
        const user = await user_model_1.default.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided ID does not exist.',
                    suggestions: ['Check the userId and try again.'],
                },
            });
        }
        const account = await accountnumber_model_1.Account.findOne({ userId: user._id });
        if (!account) {
            return res.status(404).json({
                status: false,
                message: 'ACCOUNT_NOT_FOUND',
                meta: {
                    error: 'Account for the provided user does not exist.',
                    suggestions: ['Check the userId and try again.'],
                },
            });
        }
        // Check if the account has a cardholder
        if (!account.creditCard || !account.creditCard.cardholderId) {
            return res.status(400).json({
                status: false,
                message: 'CARDHOLDER_NOT_FOUND',
                meta: {
                    error: 'No cardholder found for this account.',
                    suggestions: ['Create a cardholder first.'],
                },
            });
        }
        // Issue the card using the Stripe API
        const card = await stripeService.issueCard(account.creditCard.cardholderId, // Use the cardholderId from the creditCard schema
        type, // Type of card (virtual or physical)
        { userId: userId.toString() });
        // Save the entire card response in the creditCard object as cardDetails
        account.creditCard.cardDetails = card;
        // Save the updated account document
        await account.save();
        return res.status(201).json({
            status: true,
            message: 'CARD_ISSUED_SUCCESSFULLY',
            data: card,
        });
    }
    catch (error) {
        console.error('Error issuing card:', error);
        return res.status(500).json({
            status: false,
            message: 'CARD_ISSUANCE_FAILED',
            meta: {
                error: error.message,
                suggestions: ['Check your input data for errors.', 'Try again later.'],
            },
        });
    }
};
exports.issueCreditCard = issueCreditCard;
/**
 * Controller function to retrieve card details
 */
const retrieveCreditCard = async (req, res) => {
    const { userId } = req.params;
    // Input validation
    if (!userId) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'userId is required.',
                suggestions: ['Provide userId in the request parameters.'],
            },
        });
    }
    try {
        // Fetch the user and account details
        const user = await user_model_1.default.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided ID does not exist.',
                    suggestions: ['Check the userId and try again.'],
                },
            });
        }
        const account = await accountnumber_model_1.Account.findOne({ userId: user._id });
        if (!account) {
            return res.status(404).json({
                status: false,
                message: 'ACCOUNT_NOT_FOUND',
                meta: {
                    error: 'Account for the provided user does not exist.',
                    suggestions: ['Check the userId and try again.'],
                },
            });
        }
        // Check if the account has a cardholder and card details
        if (!account.creditCard || !account.creditCard.cardDetails) {
            return res.status(400).json({
                status: false,
                message: 'CARD_NOT_FOUND',
                meta: {
                    error: 'No card details found for this account.',
                    suggestions: ['Issue a card first.'],
                },
            });
        }
        // Retrieve the card ID from the saved card details
        const cardId = account.creditCard.cardDetails.id;
        // Fetch the card details using the Stripe API
        const cardDetails = await stripeService.getCardDetails(cardId);
        return res.status(200).json({
            status: true,
            message: 'CARD_RETRIEVED_SUCCESSFULLY',
            data: cardDetails,
        });
    }
    catch (error) {
        console.error('Error retrieving card:', error);
        return res.status(500).json({
            status: false,
            message: 'CARD_RETRIEVAL_FAILED',
            meta: {
                error: error.message,
                suggestions: ['Check your input data for errors.', 'Try again later.'],
            },
        });
    }
};
exports.retrieveCreditCard = retrieveCreditCard;
/**
 * Combined function to create a cardholder, issue a credit card, and retrieve card details.
 */
const createCardholderIssueAndRetrieveCard = async (req, res) => {
    const { userId, type, accountNumber } = req.body; // Add accountNumber to the request body
    // Input validation
    if (!userId || !type || !accountNumber) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'userId, type, and accountNumber are required.',
                suggestions: ['Provide userId, type, and accountNumber.'],
            },
        });
    }
    try {
        // Step 1: Fetch the user and account details
        const user = await user_model_1.default.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided ID does not exist.',
                    suggestions: ['Check the userId and try again.'],
                },
            });
        }
        // Fetch the account using accountNumber
        const account = await accountnumber_model_1.Account.findOne({ accountNumber }); // Use accountNumber to find the account
        if (!account) {
            return res.status(404).json({
                status: false,
                message: 'ACCOUNT_NOT_FOUND',
                meta: {
                    error: 'Account for the provided accountNumber does not exist.',
                    suggestions: ['Check the accountNumber and try again.'],
                },
            });
        }
        // Step 2: Determine the billing address based on the account type
        let billingAddress;
        if (user.accountType === 'business') {
            if (!user.business_address) {
                return res.status(400).json({
                    status: false,
                    message: 'BUSINESS_ADDRESS_REQUIRED',
                    meta: {
                        error: 'Business address is required for business accounts.',
                        suggestions: ['Update the user profile with a valid business address.'],
                    },
                });
            }
            billingAddress = user.business_address;
        }
        else {
            if (!user.home_address) {
                return res.status(400).json({
                    status: false,
                    message: 'HOME_ADDRESS_REQUIRED',
                    meta: {
                        error: 'Home address is required for client accounts.',
                        suggestions: ['Update the user profile with a valid home address.'],
                    },
                });
            }
            billingAddress = user.home_address;
        }
        // Step 3: Split the full legal name into first_name and last_name
        const nameParts = user.full_legal_name.split(' ');
        const first_name = nameParts[0];
        const last_name = nameParts.slice(1).join(' ');
        // Step 4: Extract date_of_birth and split into day, month, and year
        const date_of_birth = new Date(user.date_of_birth);
        const dob = {
            day: date_of_birth.getUTCDate(),
            month: date_of_birth.getUTCMonth() + 1,
            year: date_of_birth.getUTCFullYear(),
        };
        // Step 5: Capture user_terms_acceptance details from the request
        const user_terms_acceptance = {
            date: Math.floor(Date.now() / 1000),
            ip: req.ip,
            user_agent: req.headers['user-agent'],
        };
        // Step 6: Create a cardholder in Stripe
        const cardholder = await stripeService.createCardholder({
            name: user.full_legal_name,
            email: user.email,
            phone_number: user.phone_number,
            billing_address: {
                line1: billingAddress.line1,
                city: billingAddress.city,
                state: billingAddress.state || '',
                postal_code: billingAddress.postal_code,
                country: billingAddress.country,
            },
            metadata: {
                accountNumber: account.accountNumber, // Use the accountNumber from the account
            },
            individual: {
                first_name,
                last_name,
                dob,
                card_issuing: {
                    user_terms_acceptance,
                },
            },
        });
        // Step 7: Save the cardholder data to the Account model
        account.creditCard = {
            cardholderId: cardholder.id,
            name: cardholder.name,
            email: cardholder.email,
            phone_number: cardholder.phone_number,
            billing_address: {
                city: cardholder.billing.address.city,
                country: cardholder.billing.address.country,
                line1: cardholder.billing.address.line1,
                line2: cardholder.billing.address.line2 || '',
                postal_code: cardholder.billing.address.postal_code,
                state: cardholder.billing.address.state || '',
            },
            metadata: cardholder.metadata,
            status: cardholder.status,
            created: new Date(cardholder.created * 1000),
            cardDetails: null, // Initialize cardDetails as null
        };
        await account.save();
        // Step 8: Issue the card using the Stripe API
        const card = await stripeService.issueCard(account.creditCard.cardholderId, type, { userId: userId.toString() });
        // Step 9: Retrieve the card details using the Stripe API
        const cardDetails = await stripeService.getCardDetails(card.id);
        // Step 10: Encrypt only the sensitive fields
        const encryptedCardDetails = {
            created: encryptionService.encrypt(cardDetails.created.toString()), // Always defined
            currency: encryptionService.encrypt(cardDetails.currency), // Always defined
            cvc: cardDetails.cvc ? encryptionService.encrypt(cardDetails.cvc) : undefined, // Handle undefined
            exp_month: encryptionService.encrypt(cardDetails.exp_month.toString()), // Always defined
            exp_year: encryptionService.encrypt(cardDetails.exp_year.toString()), // Always defined
            last4: encryptionService.encrypt(cardDetails.last4), // Always defined
            number: cardDetails.number ? encryptionService.encrypt(cardDetails.number) : undefined, // Handle undefined
        };
        // Step 11: Save the encrypted card details in the database
        account.creditCard.cardDetails = encryptedCardDetails;
        await account.save();
        // Step 12: Return the full card details with sensitive fields encrypted
        const responseData = {
            ...cardDetails, // Include all card details
            created: encryptedCardDetails.created, // Overwrite with encrypted fields
            currency: encryptedCardDetails.currency,
            cvc: encryptedCardDetails.cvc,
            exp_month: encryptedCardDetails.exp_month,
            exp_year: encryptedCardDetails.exp_year,
            last4: encryptedCardDetails.last4,
            number: encryptedCardDetails.number,
        };
        return res.status(201).json({
            status: true,
            message: 'CARDHOLDER_CREATED_AND_CARD_ISSUED_SUCCESSFULLY',
            data: responseData, // Return the full card details with sensitive fields encrypted
        });
    }
    catch (error) {
        console.error('Error in combined function:', error);
        return res.status(500).json({
            status: false,
            message: 'COMBINED_FUNCTION_FAILED',
            meta: {
                error: error.message,
                suggestions: ['Check your input data for errors.', 'Try again later.'],
            },
        });
    }
};
exports.createCardholderIssueAndRetrieveCard = createCardholderIssueAndRetrieveCard;
const retrieveAndDecryptCardDetails = async (req, res) => {
    const { userId, accountNumber } = req.params;
    // Input validation
    if (!userId || !accountNumber) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'userId and accountNumber are required.',
                suggestions: ['Provide userId and accountNumber in the request parameters.'],
            },
        });
    }
    try {
        // Step 1: Fetch the user from the database
        const user = await user_model_1.default.findById(userId).exec();
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'User with the provided ID does not exist.',
                    suggestions: ['Check the userId and try again.'],
                },
            });
        }
        // Step 2: Fetch the account associated with the accountNumber
        const account = await accountnumber_model_1.Account.findOne({ accountNumber }).exec();
        // console.log('Querying Account with accountNumber:', accountNumber);
        // console.log('Account found:', account);
        if (!account || !account.creditCard || !account.creditCard.cardDetails) {
            return res.status(404).json({
                status: false,
                message: 'ACCOUNT_OR_CARD_DETAILS_NOT_FOUND',
                meta: {
                    error: 'Account or card details not found for the provided accountNumber.',
                    suggestions: ['Check the accountNumber and try again.'],
                },
            });
        }
        // Step 3: Extract the encrypted card details
        const encryptedCardDetails = account.creditCard.cardDetails;
        // Log the encrypted payload for debugging
        // console.log('Encrypted Card Details:', encryptedCardDetails);
        // Step 4: Decrypt each field in the card details
        const decryptedCardDetails = {
            created: encryptionService.decrypt(encryptedCardDetails.created),
            currency: encryptionService.decrypt(encryptedCardDetails.currency),
            cvc: encryptedCardDetails.cvc ? encryptionService.decrypt(encryptedCardDetails.cvc) : undefined,
            exp_month: encryptionService.decrypt(encryptedCardDetails.exp_month),
            exp_year: encryptionService.decrypt(encryptedCardDetails.exp_year),
            last4: encryptionService.decrypt(encryptedCardDetails.last4),
            number: encryptedCardDetails.number ? encryptionService.decrypt(encryptedCardDetails.number) : undefined,
        };
        // Step 5: Return the user and decrypted card details in the response
        return res.status(200).json({
            status: true,
            message: 'USER_AND_CARD_DETAILS_RETRIEVED_SUCCESSFULLY',
            data: {
                user: {
                    id: user._id,
                    accountType: user.accountType,
                    full_legal_name: user.full_legal_name,
                    email: user.email,
                    phone_number: user.phone_number,
                    home_address: user.home_address,
                    business_address: user.business_address,
                },
                cardDetails: decryptedCardDetails,
            },
        });
    }
    catch (error) {
        console.error('Error retrieving user and card details:', error);
        return res.status(500).json({
            status: false,
            message: 'INTERNAL_SERVER_ERROR',
            meta: {
                error: error.message,
                suggestions: ['Check your input data for errors.', 'Try again later.'],
            },
        });
    }
};
exports.retrieveAndDecryptCardDetails = retrieveAndDecryptCardDetails;
const createPaymentIntent = async (req, res) => {
    const { amount, currency = 'usd', description, paymentMethodId, userId } = req.body;
    // Validate input
    if (!amount || !paymentMethodId || !userId) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'amount, paymentMethodId, and userId are required.',
                suggestions: ['Provide amount, paymentMethodId, and userId in the body.'],
            },
        });
    }
    try {
        // Fetch user details from database
        const user = await user_model_1.default.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'No user found with the provided userId.',
                },
            });
        }
        // Construct metadata with user details
        const metadata = {
            userId: user._id.toString(),
            full_legal_name: user.full_legal_name || '',
            email: user.email || '',
            phone_number: user.phone_number || '',
        };
        // Call the StripeService to create a PaymentIntent
        const paymentIntent = await stripeService.createPaymentIntent(amount, currency, description, metadata, paymentMethodId);
        console.log('Payment Intent:', paymentIntent);
        // If payment succeeded, update the user's account balance and create a transaction
        if (paymentIntent.status === 'succeeded') {
            // Convert Stripe amount from cents to dollars
            const amountInDollars = amount / 100;
            // Find the user's account (assuming the user has only one account, or you're handling the correct account)
            const account = await accountnumber_model_1.Account.findOne({ userId });
            if (!account) {
                return res.status(404).json({
                    status: false,
                    message: 'ACCOUNT_NOT_FOUND',
                    meta: {
                        error: 'No account found for the provided userId.',
                    },
                });
            }
            // Find the USD balance in the account and update it
            const usdBalance = account.balance.find(b => b.currency === 'USD');
            if (usdBalance) {
                usdBalance.walletBalance += amountInDollars; // Add the payment amount
            }
            else {
                // If USD balance doesn't exist, create it
                account.balance.push({
                    currency: 'USD',
                    walletBalance: amountInDollars,
                    cardBalance: 0,
                });
            }
            // Save the updated account
            await account.save();
            // Generate a timestamp for the transaction
            const timestamp = new Date().toISOString().replace(/[-:T.Z]/g, '').slice(0, 14);
            // Generate a transaction ID
            const transactionId = (0, accountnumber_service_1.generateTransactionId)(`"SANGA"-"CREDITCARDFUNDING"-${timestamp}`);
            // Create a receipt for the transaction
            const receipt = (0, accountnumber_service_1.createTransactionReceipt)('SANGA', 'USD-FUNDING', timestamp, transactionId);
            // Create a transaction for the payment intent
            const transactionData = {
                transactionId: paymentIntent.id,
                type: 'CARD', // Categorize as a card transaction
                title: 'USD Card Funded Successfully',
                amount: amountInDollars,
                sender: {
                    userId: user._id,
                    name: user.full_legal_name, // Use the user's full legal name from the User model
                    accountNumber: account.accountNumber, // Use the fetched account number
                    username: user.email, // Use the user's email from the User model
                },
                predefinedDescription: 'USD Payment received',
                status: 'SUCCESS',
                timestamp: new Date(),
                cardTransaction: {
                    cardId: paymentIntent.payment_method, // Use payment method ID as card ID
                    cardType: paymentIntent.payment_method_types?.[0] || 'Unknown', // Use the first payment method type
                },
                receipt, // Include the receipt in the transaction
            };
            const transaction = new transaction_model_1.Transaction(transactionData);
            await transaction.save();
            console.log('Transaction saved for PaymentIntent:', paymentIntent.id);
            return res.status(200).json({
                status: true,
                message: 'PAYMENT_INTENT_CREATED',
                data: {
                    paymentIntentId: paymentIntent.id,
                    status: paymentIntent.status,
                    transactionId: transaction._id, // Include the transaction ID in the response
                    receipt, // Include the receipt in the response
                },
            });
        }
        return res.status(400).json({
            status: false,
            message: 'PAYMENT_FAILED',
            meta: {
                error: 'The payment did not succeed.',
            },
        });
    }
    catch (error) {
        console.error('Error creating PaymentIntent:', error);
        return res.status(500).json({
            status: false,
            message: 'INTERNAL_SERVER_ERROR',
            meta: {
                error: error.message,
                suggestions: ['Check your input data for errors.', 'Try again later.'],
            },
        });
    }
};
exports.createPaymentIntent = createPaymentIntent;
/**
 * Controller function to handle Stripe webhook events.
 */
const handleStripeWebhook = async (req, res) => {
    const sig = req.headers['stripe-signature'];
    // Ensure the Stripe signature is present
    if (!sig) {
        res.status(400).json({ error: 'Stripe signature is missing' });
        return;
    }
    try {
        // Access the raw request body
        const rawBody = req.body;
        // Ensure the signature is a string (not an array)
        const signature = Array.isArray(sig) ? sig[0] : sig;
        // Call the service method to handle the webhook event
        const event = await stripeService.handleWebhookEvent(rawBody, signature);
        // Return a success response
        res.status(200).json({ received: true, event });
    }
    catch (error) {
        console.error('Webhook Error:', error);
        res.status(400).json({ error: 'Webhook handling failed' });
    }
};
exports.handleStripeWebhook = handleStripeWebhook;
/**
 * API to create a Stripe Connected Account for a user
 */
const createStripeAccount = async (req, res) => {
    const { userId } = req.body;
    // Validate userId
    if (!userId) {
        return res.status(400).json({
            status: false,
            message: 'MISSING_REQUIRED_FIELDS',
            meta: {
                error: 'userId is required.',
                suggestions: ['Provide userId in the request body.'],
            },
        });
    }
    try {
        // Find the user by userId
        const user = await user_model_1.default.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'No user found with the provided userId.',
                    suggestions: ['Check the userId and try again.'],
                },
            });
        }
        // Check if the user already has a Stripe account ID
        if (user.stripeAccountId) {
            return res.status(200).json({
                status: true,
                message: 'STRIPE_ACCOUNT_ALREADY_EXISTS',
                data: {
                    stripeAccountId: user.stripeAccountId,
                },
            });
        }
        // Create a Stripe Connected Account for the user
        const account = await stripeService.createConnectedAccount({
            email: user.email, // User's email
            country: user.home_address.country, // User's country of residence
            businessType: 'individual', // Default to 'individual'
            individual: {
                first_name: user.first_name,
                last_name: user.last_name,
                dob: {
                    day: user.date_of_birth.getDate(),
                    month: user.date_of_birth.getMonth() + 1, // Months are 0-indexed in JavaScript
                    year: user.date_of_birth.getFullYear(),
                },
            },
            tosAcceptance: {
                date: Math.floor(Date.now() / 1000), // Current Unix timestamp
                ip: req.ip || '0.0.0.0', // Fallback to '0.0.0.0' if req.ip is undefined
            },
        });
        // Save the Stripe account ID to the user's profile
        user.stripeAccountId = account.id;
        await user.save();
        // Return success response
        return res.status(200).json({
            status: true,
            message: 'STRIPE_ACCOUNT_CREATED_SUCCESSFULLY',
            data: {
                stripeAccountId: account.id,
            },
        });
    }
    catch (error) {
        console.error('Error creating Stripe account:', error);
        // Handle specific Stripe errors
        if (error instanceof stripe_1.default.errors.StripeError) {
            return res.status(400).json({
                status: false,
                message: 'E_ERROR',
                meta: {
                    error: error,
                    suggestions: ['Check the input data and try again.'],
                },
            });
        }
        // Handle generic errors
        return res.status(500).json({
            status: false,
            message: 'INTERNAL_SERVER_ERROR',
            meta: {
                error: error.message,
                suggestions: ['Try again later.'],
            },
        });
    }
};
exports.createStripeAccount = createStripeAccount;
const linkCard = async (req, res) => {
    const { token, userId } = req.body;
    try {
        // Find the user by userId
        const user = await user_model_1.default.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'No user found with the provided userId.',
                    suggestions: ['Check the userId and try again.'],
                },
            });
        }
        // Check if the user has a Stripe account ID
        if (!user.stripeAccountId) {
            return res.status(400).json({
                status: false,
                message: 'STRIPE_ACCOUNT_NOT_FOUND',
                meta: {
                    error: 'User does not have a Stripe account.',
                    suggestions: ['Create a Stripe account for the user first.'],
                },
            });
        }
        // Link the card to the user's Stripe Connected Account
        const externalAccount = await stripeService.linkCardToAccount(user.stripeAccountId, token);
        // Save the external account ID to the user's profile (optional)
        user.externalStripeAccountId = externalAccount.id;
        await user.save();
        // Return success response
        return res.status(200).json({
            status: true,
            message: 'CARD_LINKED_SUCCESSFULLY',
            data: {
                externalAccountId: externalAccount.id,
            },
        });
    }
    catch (error) {
        console.error('Error linking card:', error);
        // Handle specific Stripe errors
        if (error instanceof stripe_1.default.errors.StripeError) {
            return res.status(400).json({
                status: false,
                message: 'STRIPE_ERROR',
                meta: {
                    error: error.message,
                    suggestions: ['Check the input data and try again.'],
                },
            });
        }
        // Handle generic errors
        return res.status(500).json({
            status: false,
            message: 'INTERNAL_SERVER_ERROR',
            meta: {
                error: error.message,
                suggestions: ['Try again later.'],
            },
        });
    }
};
exports.linkCard = linkCard;
