"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getClientCredentials = exports.refreshAccessTokenController = exports.generateAccessToken = exports.decodeToken = exports.generateGrantToken = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const token_service_1 = __importDefault(require("../apisystem/service/token.service"));
const validator_1 = __importDefault(require("validator"));
const user_model_1 = __importDefault(require("../models/user.model"));
const tokenService = new token_service_1.default();
// Helper function to sanitize and validate input
const sanitizeInput = (input) => {
    return validator_1.default.escape(validator_1.default.trim(input));
};
// Controller for generating a grant token
const generateGrantToken = async (req, res) => {
    try {
        const { expiresIn, permissions } = req.body;
        // Basic validation
        if (typeof expiresIn !== 'number' || expiresIn <= 0) {
            return res.status(400).json({
                status: false,
                message: 'INVALID_EXPIRES_IN',
                meta: {
                    error: 'expiresIn must be a positive number.',
                    suggestions: ['Provide a valid expiresIn value in the request body.'],
                },
            });
        }
        if (!Array.isArray(permissions) || permissions.length === 0) {
            return res.status(400).json({
                status: false,
                message: 'INVALID_PERMISSIONS',
                meta: {
                    error: 'permissions must be a non-empty array.',
                    suggestions: ['Provide a valid permissions array in the request body.'],
                },
            });
        }
        const payload = {
            timestamp: Date.now(),
        };
        // Generate the token
        const token = tokenService.generateGrantToken(payload, expiresIn, permissions);
        // Return the token in the response
        return res.status(200).json({
            status: true,
            message: 'GRANT_TOKEN_GENERATED',
            data: {
                token,
            },
        });
    }
    catch (error) {
        console.error('Error generating token:', error);
        return res.status(500).json({
            status: false,
            message: 'TOKEN_GENERATION_FAILED',
            meta: {
                error: error.message,
                suggestions: ['Ensure the request payload is valid and try again.'],
            },
        });
    }
};
exports.generateGrantToken = generateGrantToken;
// Controller for decoding and validating a token
const decodeToken = async (req, res) => {
    try {
        const { token } = req.body;
        if (!token) {
            return res.status(400).json({
                status: false,
                message: 'MISSING_REQUIRED_FIELDS',
                meta: {
                    error: 'Token is required.',
                    suggestions: ['Provide the token in the request body.'],
                },
            });
        }
        // Validate and decode the token
        const decodedToken = tokenService.validateGrantToken(token);
        if (decodedToken) {
            return res.status(200).json({
                status: true,
                message: 'TOKEN_DECODED',
                data: {
                    decodedToken,
                },
            });
        }
        else {
            return res.status(400).json({
                status: false,
                message: 'INVALID_TOKEN',
                meta: {
                    error: 'Invalid or expired token.',
                    suggestions: ['Provide a valid token.'],
                },
            });
        }
    }
    catch (error) {
        console.error('Error decoding token:', error);
        // Handle specific errors from TokenService
        if (error instanceof Error) {
            return res.status(400).json({
                status: false,
                message: 'TOKEN_VALIDATION_FAILED',
                meta: {
                    error: error.message,
                    suggestions: ['Check the token and try again.'],
                },
            });
        }
        else {
            return res.status(500).json({
                status: false,
                message: 'INTERNAL_SERVER_ERROR',
                meta: {
                    error: 'Failed to decode token.',
                    suggestions: ['Try again later.'],
                },
            });
        }
    }
};
exports.decodeToken = decodeToken;
// Controller for generating an access token
const generateAccessToken = async (req, res) => {
    try {
        const { userId, grant_type, client_id, client_secret, redirect_uri, grantToken } = req.body;
        // Sanitize inputs
        const sanitizedUserId = sanitizeInput(userId);
        const sanitizedClientId = sanitizeInput(client_id);
        const sanitizedClientSecret = sanitizeInput(client_secret);
        const sanitizedRedirectUri = redirect_uri ? sanitizeInput(redirect_uri) : undefined;
        const sanitizedGrantToken = sanitizeInput(grantToken);
        // Validate required fields
        if (!sanitizedUserId || !grant_type || !sanitizedClientId || !sanitizedClientSecret || !sanitizedGrantToken) {
            return res.status(400).json({
                status: false,
                message: 'MISSING_REQUIRED_FIELDS',
                meta: {
                    error: 'userId, grant_type, client_id, client_secret, and grantToken are required.',
                    suggestions: ['Provide all required fields in the request body.'],
                },
            });
        }
        // Validate userId length (24 characters for MongoDB ObjectId)
        if (sanitizedUserId.length !== 24) {
            return res.status(400).json({
                status: false,
                message: 'INVALID_USER_ID',
                meta: {
                    error: 'The provided userId is invalid or does not match the required length of 24 characters.',
                    suggestions: ['Verify the userId format and try again.'],
                },
            });
        }
        // Query the user by userId
        const user = await user_model_1.default.findById(sanitizedUserId).exec();
        // Check if the user exists
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'No user found with the provided userId.',
                    suggestions: ['Verify the userId and try again.'],
                },
            });
        }
        // Verify client_id and client_secret for the user
        if (user.client_id !== sanitizedClientId || user.client_secret !== sanitizedClientSecret) {
            return res.status(401).json({
                status: false,
                message: 'INVALID_CLIENT_CREDENTIALS',
                meta: {
                    error: 'The provided client_id or client_secret is incorrect for the given user.',
                    suggestions: ['Verify the client_id and client_secret and try again.'],
                },
            });
        }
        // Ensure the redirect_uri matches if provided
        if (sanitizedRedirectUri && user.redirect_uri !== sanitizedRedirectUri) {
            return res.status(400).json({
                status: false,
                message: 'INVALID_REDIRECT_URI',
                meta: {
                    error: 'The provided redirect_uri does not match the one associated with the user.',
                    suggestions: ['Verify the redirect_uri and try again.'],
                },
            });
        }
        // Validate and decode the grant token
        const decodedGrantToken = tokenService.validateGrantToken(sanitizedGrantToken);
        if (!decodedGrantToken) {
            return res.status(400).json({
                status: false,
                message: 'INVALID_GRANT_TOKEN',
                meta: {
                    error: 'The provided grantToken is invalid or has expired.',
                    suggestions: ['Provide a valid grantToken and try again.'],
                },
            });
        }
        // Generate the access token with userId in the payload
        const accessTokenResponse = tokenService.generateAccessToken(grant_type, sanitizedClientId, sanitizedClientSecret, sanitizedGrantToken);
        // Return the access token response
        return res.status(200).json({
            status: true,
            message: 'ACCESS_TOKEN_GENERATED',
            data: accessTokenResponse,
        });
    }
    catch (error) {
        console.error('Error generating access token:', error);
        // Handle specific errors from TokenService
        if (error instanceof Error) {
            return res.status(400).json({
                status: false,
                message: 'ACCESS_TOKEN_GENERATION_FAILED',
                meta: {
                    error: error.message,
                    suggestions: ['Check the input data and try again.'],
                },
            });
        }
        else {
            return res.status(500).json({
                status: false,
                message: 'INTERNAL_SERVER_ERROR',
                meta: {
                    error: 'Failed to generate access token.',
                    suggestions: ['Try again later.'],
                },
            });
        }
    }
};
exports.generateAccessToken = generateAccessToken;
// Controller for refreshing an access token
const refreshAccessTokenController = async (req, res) => {
    try {
        const { userId, refresh_token, client_id, client_secret, grant_type } = req.body;
        // Sanitize inputs
        const sanitizedUserId = sanitizeInput(userId);
        const sanitizedRefreshToken = sanitizeInput(refresh_token);
        const sanitizedClientId = sanitizeInput(client_id);
        const sanitizedClientSecret = sanitizeInput(client_secret);
        // Validate required fields
        if (!sanitizedUserId || !sanitizedRefreshToken || !sanitizedClientId || !sanitizedClientSecret || !grant_type) {
            return res.status(400).json({
                status: false,
                message: 'MISSING_REQUIRED_FIELDS',
                meta: {
                    error: 'userId, refresh_token, client_id, client_secret, and grant_type are required.',
                    suggestions: ['Provide all required fields in the request body.'],
                },
            });
        }
        // Validate userId length (24 characters for MongoDB ObjectId)
        if (sanitizedUserId.length !== 24) {
            return res.status(400).json({
                status: false,
                message: 'INVALID_USER_ID',
                meta: {
                    error: 'The provided userId is invalid or does not match the required length of 24 characters.',
                    suggestions: ['Verify the userId format and try again.'],
                },
            });
        }
        // Query the user by userId
        const user = await user_model_1.default.findById(sanitizedUserId).exec();
        // Check if the user exists
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'No user found with the provided userId.',
                    suggestions: ['Verify the userId and try again.'],
                },
            });
        }
        // Validate grant_type
        if (grant_type !== 'refresh_token') {
            return res.status(400).json({
                status: false,
                message: 'INVALID_GRANT_TYPE',
                meta: {
                    error: 'The provided grant_type is invalid. Expected "refresh_token".',
                    suggestions: ['Provide a valid grant_type and try again.'],
                },
            });
        }
        // Verify client_secret for the user
        if (user.client_secret !== sanitizedClientSecret) {
            return res.status(401).json({
                status: false,
                message: 'INVALID_CLIENT_CREDENTIALS',
                meta: {
                    error: 'The provided client_secret is incorrect for the given user.',
                    suggestions: ['Verify the client_secret and try again.'],
                },
            });
        }
        // Generate a new access token using the refresh token
        const accessTokenResponse = tokenService.refreshAccessToken(sanitizedRefreshToken, sanitizedClientId, sanitizedClientSecret, grant_type);
        // Return the access token response
        return res.status(200).json({
            status: true,
            message: 'ACCESS_TOKEN_REFRESHED',
            data: accessTokenResponse,
        });
    }
    catch (error) {
        console.error('Error refreshing access token:', error);
        // Handle specific errors from TokenService
        if (error instanceof Error) {
            return res.status(400).json({
                status: false,
                message: 'ACCESS_TOKEN_REFRESH_FAILED',
                meta: {
                    error: error.message,
                    suggestions: ['Check the input data and try again.'],
                },
            });
        }
        else {
            return res.status(500).json({
                status: false,
                message: 'INTERNAL_SERVER_ERROR',
                meta: {
                    error: 'Failed to refresh access token.',
                    suggestions: ['Try again later.'],
                },
            });
        }
    }
};
exports.refreshAccessTokenController = refreshAccessTokenController;
// Controller to get client_id and client_secret by user ID
const getClientCredentials = async (req, res) => {
    const { userId } = req.params;
    // Sanitize userId
    const sanitizedUserId = sanitizeInput(userId);
    // Validate userId
    if (!sanitizedUserId || !mongoose_1.default.Types.ObjectId.isValid(sanitizedUserId)) {
        return res.status(400).json({
            status: false,
            message: 'INVALID_USER_ID',
            meta: {
                error: 'The provided userId is missing or not a valid ObjectId.',
                suggestions: ['Provide a valid userId and try again.'],
            },
        });
    }
    try {
        // Find the user by ID
        const user = await user_model_1.default.findById(sanitizedUserId).select('client_id client_secret').exec();
        // Check if the user exists
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'No user found with the provided userId.',
                    suggestions: ['Verify the userId and try again.'],
                },
            });
        }
        // Check if client_id and client_secret exist
        if (!user.client_id || !user.client_secret) {
            return res.status(404).json({
                status: false,
                message: 'CLIENT_CREDENTIALS_NOT_FOUND',
                meta: {
                    error: 'The user does not have client_id or client_secret.',
                    suggestions: ['Ensure the user has client credentials and try again.'],
                },
            });
        }
        // Return the client_id and client_secret
        return res.status(200).json({
            status: true,
            message: 'CLIENT_CREDENTIALS_RETRIEVED',
            data: {
                client_id: user.client_id,
                client_secret: user.client_secret,
            },
        });
    }
    catch (error) {
        console.error('Error retrieving client credentials:', error);
        return res.status(500).json({
            status: false,
            message: 'INTERNAL_SERVER_ERROR',
            meta: {
                error: error.message,
                suggestions: ['Try again later.'],
            },
        });
    }
};
exports.getClientCredentials = getClientCredentials;
