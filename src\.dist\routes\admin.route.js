"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const admin_controller_1 = require("../controllers/admin.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const adminRouter = express_1.default.Router();
adminRouter.post('/upgrade-account/:userId', auth_middleware_1.authenticateToken, admin_controller_1.upgradeAccountToBusiness);
exports.default = adminRouter;
