import { Request, Response } from 'express';
import mongoose from 'mongoose';
import validator from 'validator';
import User, { IKycDocument, IUser, KycCategory, KycDocumentType } from '../models/user.model';
import { Account } from '../models/accountnumber.model';
import { unifiedUploader } from '../services/uploader.service';
import bcrypt from 'bcrypt';
import Beneficiary from '../models/beneficiary.model';
import twilio from 'twilio';
import Verification from '../models/verification.model';

const sanitizeInput = (input: string): string => {
  return validator.escape(validator.trim(input));
};

const twilioClient = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

export const updateProfile = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const updateData = req.body;

  // Validate userId
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'The provided userId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid userId and try again.'],
      },
    });
  }

  try {
    // Find the user by userId
    const user: IUser | null = await User.findById(userId).exec();

    // Check if the user exists
    if (!user) {
      return res.status(404).json({
        status: false,
        message: 'USER_NOT_FOUND',
        meta: {
          error: 'No user found with the provided userId.',
          suggestions: ['Verify the userId and try again.'],
        },
      });
    }

    // Sanitize and validate update data
    const sanitizedUpdateData: Partial<IUser> = {};

    for (const [key, value] of Object.entries(updateData)) {
      if (typeof value === 'string') {
        sanitizedUpdateData[key as keyof IUser] = sanitizeInput(value);
      } else {
        sanitizedUpdateData[key as keyof IUser] = value;
      }
    }

    // Prevent updating sensitive or restricted fields
    const restrictedFields = [
      'password',
      'client_id',
      'client_secret',
      'role',
      'permissions',
      'phone_number', // Add phone_number to the restricted fields
    ];

    for (const field of restrictedFields) {
      if (sanitizedUpdateData.hasOwnProperty(field)) {
        return res.status(400).json({
          status: false,
          message: 'RESTRICTED_FIELD',
          meta: {
            error: `The field "${field}" cannot be updated via this endpoint.`,
            suggestions: ['Remove restricted fields from the update data.'],
          },
        });
      }
    }

    // Validate home_address if present in updateData
    if (sanitizedUpdateData.home_address) {
      const { line1, city, state, postal_code, country } = sanitizedUpdateData.home_address;
      if (!line1 || !city || !state || !postal_code || !country) {
        return res.status(400).json({
          status: false,
          message: 'INVALID_HOME_ADDRESS',
          meta: {
            error: 'All fields in home_address (line1, city, state, postal_code, country) are required.',
            suggestions: ['Provide all required fields for home_address.'],
          },
        });
      }
    }

    // Update the user's profile
    Object.assign(user, sanitizedUpdateData);

    // Save the updated user
    await user.save();

    // Return the updated user profile
    return res.status(200).json({
      status: true,
      message: 'PROFILE_UPDATED_SUCCESSFULLY',
      data: {
        id: user._id,
        accountType: user.accountType,
        first_name: user.first_name, // Include first_name in the response
        last_name: user.last_name, // Include last_name in the response
        full_legal_name: user.full_legal_name,
        date_of_birth: user.date_of_birth,
        home_address: user.home_address,
        phone_number: user.phone_number, // Include the phone_number in the response (read-only)
        country_of_residence: user.country_of_residence,
        email: user.email,
        kyc_status: user.kyc_status,
        userIsVerified: user.userIsVerified,
        documents: user.documents,
        // Add other fields as needed
      },
    });
  } catch (error) {
    console.error('Error updating user profile:', error);

    return res.status(500).json({
      status: false,
      message: 'PROFILE_UPDATE_FAILED',
      meta: {
        error: (error as Error).message,
        suggestions: [
          'Check the input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};



export const getUserDetails = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;

  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'The provided userId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid userId and try again.']
      }
    });
  }

  try {
    const user = await User.findById(userId).select('-password -client_id -client_secret -redirect_uri -loginPublicKey -verificationCode -verificationCodeExpiry');

    if (!user) {
      return res.status(404).json({
        status: false,
        message: 'USER_NOT_FOUND',
        meta: {
          error: 'User with the provided ID does not exist.',
          suggestions: ['Check the user ID and try again.']
        }
      });
    }

    const account = await Account.findOne({ userId }).select('+pin');

    const accountData = account ? {
      cardholderId: account.creditCard?.cardholderId || null,
      accountNumber: account.accountNumber || null,
      balance: account.balance.map(b => ({
        currency: b.currency,
        amount: b.amount !== undefined ? parseFloat(b.amount.toFixed(2)) : 0,
        walletBalance: parseFloat(b.walletBalance.toFixed(2)),
        cardBalance: parseFloat(b.cardBalance.toFixed(2)),
      })),
      hasSetPin: !!account.pin,
      accountStatus: account.status || 'active'
    } : null;

    const kycDocuments = {
      personal: {
        government_id: user.kyc?.personal?.government_id || [],
        passport: user.kyc?.personal?.passport || [],
        drivers_license: user.kyc?.personal?.drivers_license || [],
        national_id: user.kyc?.personal?.national_id || [],
        personalKycStatus: calculateKycSectionStatus('personal', user.kyc?.personal)
      },
      business: {
        business_registration: user.kyc?.business?.business_registration || [],
        proof_of_business: user.kyc?.business?.proof_of_business || [],
        tax_document: user.kyc?.business?.tax_document || [],
        businessKycStatus: calculateKycSectionStatus('business', user.kyc?.business)
      }
    };

    const overallKycStatus = calculateOverallKycStatus(user);

    return res.status(200).json({
      status: true,
      message: 'USER_DETAILS_FETCHED_SUCCESSFULLY',
      data: {
        user: {
          _id: user._id,
          accountType: user.accountType,
          full_legal_name: user.full_legal_name,
          first_name: user.first_name,
          last_name: user.last_name,
          date_of_birth: user.date_of_birth,
          home_address: user.home_address,
          phone_number: user.phone_number,
          social_security: user.social_security,
          country_of_residence: user.country_of_residence,
          last_login: user.last_login,
          legal_business_name: user.legal_business_name,
          business_address: user.business_address,
          type_of_business_entity: user.type_of_business_entity,
          tax_identification_number: user.tax_identification_number,
          country_of_business_residence: user.country_of_business_residence,
          bank_account_details: user.bank_account_details,
          estimated_transaction_volume: user.estimated_transaction_volume,
          email: user.email,
          kyc: {
            overallKycStatus,
            personalKycStatus: kycDocuments.personal.personalKycStatus,
            businessKycStatus: kycDocuments.business.businessKycStatus,
            documents: {
              personal: {
                government_id: kycDocuments.personal.government_id,
                passport: kycDocuments.personal.passport,
                drivers_license: kycDocuments.personal.drivers_license,
                national_id: kycDocuments.personal.national_id
              },
              business: {
                business_registration: kycDocuments.business.business_registration,
                proof_of_business: kycDocuments.business.proof_of_business,
                tax_document: kycDocuments.business.tax_document
              }
            }
          },
          userIsVerified: user.userIsVerified,
          role: user.role,
          permissions: user.permissions,
          profilePicture: user.profilePicture || null,
          documents: user.documents || []
        },
        account: accountData
      }
    });

  } catch (error: any) {
    console.error('Error during fetching user details:', error);
    return res.status(500).json({
      status: false,
      message: 'USER_DETAILS_FETCH_FAILED',
      meta: {
        error: error.message,
        suggestions: [
          'Check your input data for errors.',
          'Ensure the database is running.',
          'Try again later.'
        ]
      }
    });
  }
};

// Helper functions to calculate KYC status
const calculateKycSectionStatus = (category: string, section: any): string => {
  if (!section) return 'not_submitted';

  const hasDocuments = Object.keys(section).some(key => 
    Array.isArray(section[key]) && section[key].length > 0
  );

  if (!hasDocuments) return 'not_submitted';

  const allDocumentsPending = Object.keys(section).every(key => 
    !Array.isArray(section[key]) || 
    section[key].every((doc: any) => doc.status === 'pending')
  );

  const someDocumentsVerified = Object.keys(section).some(key => 
    Array.isArray(section[key]) && 
    section[key].some((doc: any) => doc.status === 'approved')
  );

  if (someDocumentsVerified) return 'verified';
  if (allDocumentsPending) return 'pending';
  return 'pending';
};

const calculateOverallKycStatus = (user: IUser): string => {
  if (!user.kyc) return 'not_submitted';

  const personalStatus = calculateKycSectionStatus('personal', user.kyc.personal);
  const businessStatus = calculateKycSectionStatus('business', user.kyc.business);

  if (personalStatus === 'not_submitted' && businessStatus === 'not_submitted') {
    return 'not_submitted';
  }

  if (personalStatus === 'verified' && businessStatus === 'verified') {
    return 'verified';
  }

  return 'pending';
};

export const updateProfilePicture = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const files = req.files as Express.Multer.File[];

  console.log('Files:', files);

  if (!files || files.length === 0) {
    return res.status(400).json({
      status: false,
      message: 'NO_FILE_UPLOADED',
      meta: {
        error: 'No profile picture file was uploaded',
        suggestions: ['Select a profile picture file to upload']
      }
    });
  }

  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'Invalid or missing user ID',
        suggestions: ['Provide a valid user ID'],
      },
    });
  }

  try {
    const uploadedUrls = await unifiedUploader(req, 'profilePicture');
    
    if (!uploadedUrls.length) {
      return res.status(400).json({
        status: false,
        message: 'UPLOAD_FAILED',
        meta: {
          error: 'No profile picture uploaded',
          suggestions: ['Ensure you selected a valid image file'],
        },
      });
    }

    const user = await User.findByIdAndUpdate(
      userId,
      {
        profilePicture: {
          url: uploadedUrls[0],
          updatedAt: new Date(),
        },
      },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({
        status: false,
        message: 'USER_NOT_FOUND',
        meta: { error: 'User not found' },
      });
    }

    return res.status(200).json({
      status: true,
      message: 'PROFILE_PICTURE_UPDATED',
      data: {
        profilePicture: user.profilePicture,
      },
    });
  } catch (error: any) {
    console.error('Profile picture update error:', error);
    return res.status(500).json({
      status: false,
      message: 'UPDATE_FAILED',
      meta: {
        error: error.message,
        suggestions: ['Try again later', 'Contact support if the issue persists'],
      },
    });
  }
};

export const uploadKycDocuments = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const { documentType, category } = req.body;
  const files = req.files as Express.Multer.File[];

  console.log('Files:', files, 'Body:', req.body);

  // Validate files
  if (!files || files.length === 0) {
    return res.status(400).json({
      status: false,
      message: 'NO_FILES_UPLOADED',
      meta: {
        error: 'No KYC documents were uploaded',
        suggestions: ['Select at least one document file to upload']
      }
    });
  }

  // Validate user ID
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'Invalid or missing user ID',
        suggestions: ['Provide a valid user ID'],
      },
    });
  }

  // Validate document metadata
  if (!documentType || !category) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_REQUIRED_FIELDS',
      meta: {
        error: 'Document type and category are required',
        suggestions: ['Provide documentType and category in the request body'],
      },
    });
  }

  try {
    // Upload files using unified uploader
    const uploadedUrls = await unifiedUploader(req, 'kyc');
    
    if (!uploadedUrls.length) {
      return res.status(400).json({
        status: false,
        message: 'UPLOAD_FAILED',
        meta: {
          error: 'Document upload failed',
          suggestions: ['Ensure you selected valid document files'],
        },
      });
    }

    // Create documents array
    const newDocuments: IKycDocument[] = uploadedUrls.map(url => ({
      type: documentType as KycDocumentType,
      category: category as KycCategory,
      url,
      verified: false,
      uploadedAt: new Date(),
      status: 'pending',
    }));

    // Update user document
    const updateQuery = {
      $push: {
        [`kyc.${category}.${documentType}`]: { $each: newDocuments },
        kycDocuments: { $each: newDocuments }
      },
      $set: {
        [`kyc.${category}.status`]: 'pending',
        kyc_status: 'pending'
      }
    };

    const user = await User.findByIdAndUpdate(
      userId,
      updateQuery,
      { new: true, runValidators: true }
    );

    if (!user) {
      return res.status(404).json({
        status: false,
        message: 'USER_NOT_FOUND',
        meta: { error: 'User not found' },
      });
    }

    return res.status(200).json({
      status: true,
      message: 'KYC_DOCUMENTS_UPLOADED',
      data: {
        documents: newDocuments,
        category,
        documentType,
        totalUploaded: newDocuments.length,
        kycStatus: {
          overall: user.kyc_status,
          [category]: user.kyc[category as 'personal' | 'business'].status
        }
      },
    });
  } catch (error: any) {
    console.error('KYC documents upload error:', error);
    return res.status(500).json({
      status: false,
      message: 'UPLOAD_FAILED',
      meta: {
        error: error.message,
        suggestions: ['Try again later', 'Contact support if the issue persists'],
      },
    });
  }
};


export const submitBusinessDetails = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const {
    legal_business_name,
    business_address,
    type_of_business_entity,
    tax_identification_number,
    country_of_business_residence,
    bank_account_details,
    estimated_transaction_volume,
  } = req.body;

  // Validate userId
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'The provided userId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid userId and try again.'],
      },
    });
  }

  // Validate required fields
  if (!legal_business_name) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_REQUIRED_FIELDS',
      meta: {
        error: 'Legal business name is required.',
        suggestions: ['Provide a valid legal business name.'],
      },
    });
  }

  if (!type_of_business_entity) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_REQUIRED_FIELDS',
      meta: {
        error: 'Type of business entity is required.',
        suggestions: ['Provide a valid type of business entity.'],
      },
    });
  }

  if (!tax_identification_number) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_REQUIRED_FIELDS',
      meta: {
        error: 'Tax identification number is required.',
        suggestions: ['Provide a valid tax identification number.'],
      },
    });
  }

  if (!country_of_business_residence) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_REQUIRED_FIELDS',
      meta: {
        error: 'Country of business residence is required.',
        suggestions: ['Provide a valid country of business residence.'],
      },
    });
  }

  if (!bank_account_details) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_REQUIRED_FIELDS',
      meta: {
        error: 'Bank account details are required.',
        suggestions: ['Provide valid bank account details.'],
      },
    });
  }

  if (!estimated_transaction_volume) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_REQUIRED_FIELDS',
      meta: {
        error: 'Estimated transaction volume is required.',
        suggestions: ['Provide a valid estimated transaction volume.'],
      },
    });
  }

  if (!business_address || !business_address.line1 || !business_address.city || !business_address.state || !business_address.postal_code || !business_address.country) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_REQUIRED_FIELDS',
      meta: {
        error: 'All fields in business_address (line1, city, state, postal_code, country) are required.',
        suggestions: ['Provide all required fields for business_address.'],
      },
    });
  }

  try {
    // Find the user by userId
    const user: IUser | null = await User.findById(userId).exec();

    // Check if the user exists
    if (!user) {
      return res.status(404).json({
        status: false,
        message: 'USER_NOT_FOUND',
        meta: {
          error: 'No user found with the provided userId.',
          suggestions: ['Verify the userId and try again.'],
        },
      });
    }

    // Check if the legal_business_name is already set and cannot be changed
    if (user.legal_business_name && user.legal_business_name !== legal_business_name) {
      return res.status(400).json({
        status: false,
        message: 'LEGAL_BUSINESS_NAME_IMMUTABLE',
        meta: {
          error: 'The legal business name cannot be changed once it has been set.',
          suggestions: ['Contact support if you need to update the legal business name.'],
        },
      });
    }

    // Check if the legal_business_name is already in use by another user
    const existingUserWithSameBusinessName = await User.findOne({
      legal_business_name: legal_business_name,
      _id: { $ne: userId }, // Exclude the current user from the check
    }).exec();

    if (existingUserWithSameBusinessName) {
      return res.status(400).json({
        status: false,
        message: 'DUPLICATE_BUSINESS_NAME',
        meta: {
          error: 'The provided legal business name is already in use by another user.',
          suggestions: ['Provide a unique legal business name.'],
        },
      });
    }

    // Check for existing KYC documents
    const hasBusinessKyc = user.kyc?.business?.business_registration?.length > 0 ||
                          user.kyc?.business?.proof_of_business?.length > 0 ||
                          user.kyc?.business?.tax_document?.length > 0;

    // Update business details
    const updateData = {
      legal_business_name,
      type_of_business_entity,
      tax_identification_number,
      country_of_business_residence,
      bank_account_details,
      estimated_transaction_volume,
      business_address,
      'kyc.business.status': hasBusinessKyc ? 'pending' : 'not_submitted',
      kyc_status: hasBusinessKyc ? 'pending' : 'not_submitted'
    };

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    if (!updatedUser) {
      return res.status(404).json({
        status: false,
        message: 'USER_NOT_FOUND',
        meta: { error: 'User not found' },
      });
    }

    return res.status(200).json({
      status: true,
      message: 'BUSINESS_DETAILS_UPDATED_SUCCESSFULLY',
      data: {
        id: updatedUser._id,
        accountType: updatedUser.accountType,
        legal_business_name: updatedUser.legal_business_name,
        business_address: updatedUser.business_address,
        type_of_business_entity: updatedUser.type_of_business_entity,
        tax_identification_number: updatedUser.tax_identification_number,
        country_of_business_residence: updatedUser.country_of_business_residence,
        bank_account_details: updatedUser.bank_account_details,
        estimated_transaction_volume: updatedUser.estimated_transaction_volume,
        kyc_status: updatedUser.kyc_status,
        business_kyc_status: updatedUser.kyc.business.status
      },
    });
  } catch (error) {
    console.error('Error updating business details:', error);

    return res.status(500).json({
      status: false,
      message: 'BUSINESS_DETAILS_UPDATE_FAILED',
      meta: {
        error: (error as Error).message,
        suggestions: [
          'Check the input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};




/**
 * Set a 4-digit PIN for the account.
 */
export const setPin = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const { pin } = req.body;

  // Validate userId
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'The provided userId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid userId and try again.'],
      },
    });
  }

  // Validate PIN (must be 4 digits)
  if (!/^\d{4}$/.test(pin)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_PIN',
      meta: {
        error: 'PIN must be a 4-digit number.',
        suggestions: ['Provide a valid 4-digit PIN.'],
      },
    });
  }

  try {
    // Find the account
    const account = await Account.findOne({ userId });

    if (!account) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'No account exists for the given userId.',
          suggestions: ['Check the userId or create an account first.'],
        },
      });
    }

    // Hash the PIN
    const saltRounds = 10;
    const hashedPin = await bcrypt.hash(pin, saltRounds);

    // Set the hashed PIN
    account.pin = hashedPin;
    await account.save();

    return res.status(200).json({
      status: true,
      message: 'PIN_SET_SUCCESSFULLY',
      data: {
        userId: account.userId,
        accountNumber: account.accountNumber,
      },
    });
  } catch (error: any) {
    console.error('Error setting PIN:', error);

    return res.status(500).json({
      status: false,
      message: 'PIN_SET_FAILED',
      meta: {
        error: error.message,
        suggestions: [
          'Check the input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};

/**
 * Verify the 4-digit PIN for the account.
 */
export const verifyPin = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const { pin } = req.body;

  // Validate userId
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'The provided userId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid userId and try again.'],
      },
    });
  }

  // Validate PIN (must be 4 digits)
  if (!/^\d{4}$/.test(pin)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_PIN',
      meta: {
        error: 'PIN must be a 4-digit number.',
        suggestions: ['Provide a valid 4-digit PIN.'],
      },
    });
  }

  try {
    // Find the account and explicitly select the PIN field
    const account = await Account.findOne({ userId }).select('+pin').exec();
    const user = await User.findById(userId);

    if (!account || !user) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_OR_USER_NOT_FOUND',
        meta: {
          error: 'No account or user exists for the given userId.',
          suggestions: ['Check the userId or create an account first.'],
        },
      });
    }

    if (!account.pin) {
      return res.status(400).json({
        status: false,
        message: 'PIN_NOT_SET',
        meta: {
          error: 'No PIN has been set for this account.',
          suggestions: ['Set a PIN first and try again.'],
        },
      });
    }

    // Verify the PIN
    const isMatch = await bcrypt.compare(pin, account.pin);

    if (!isMatch) {
      return res.status(400).json({
        status: false,
        message: 'INCORRECT_PIN',
        meta: {
          error: 'The provided PIN is incorrect.',
          suggestions: ['Check the PIN and try again.'],
        },
      });
    }

    // Generate verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    const verificationCodeExpiry = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes expiry

    // Save verification code
    const verification = new Verification({
      userId,
      verificationCode,
      verificationCodeExpiry,
      purpose: 'PAYMENT',
      isUsed: false
    });
    await verification.save();

    // Send SMS
    await twilioClient.messages.create({
      body: `Your payment verification code is: ${verificationCode}. It will expire in 5 minutes.`,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: user.phone_number
    });

    return res.status(200).json({
      status: true,
      message: 'PIN_VERIFIED_AND_CODE_SENT',
      data: {
        userId: account.userId,
        accountNumber: account.accountNumber,
        verificationCodeExpiry,
        message: 'Please check your phone for the verification code to complete the payment.'
      },
    });
  } catch (error: any) {
    console.error('Error in PIN verification process:', error);

    return res.status(500).json({
      status: false,
      message: 'PIN_VERIFICATION_PROCESS_FAILED',
      meta: {
        error: error.message,
        suggestions: [
          'Check the input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};

// Add a new function to verify the SMS code
export const verifyPaymentCode = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const { verificationCode } = req.body;

  try {
    const verification = await Verification.findOne({
      userId,
      verificationCode,
      purpose: 'PAYMENT',
      isUsed: false,
      verificationCodeExpiry: { $gt: new Date() }
    });

    if (!verification) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_OR_EXPIRED_CODE',
        meta: {
          error: 'The verification code is invalid or has expired.',
          suggestions: ['Check the code or request a new one.'],
        },
      });
    }

    // Mark the code as used
    verification.isUsed = true;
    await verification.save();

    return res.status(200).json({
      status: true,
      message: 'PAYMENT_VERIFICATION_SUCCESSFUL',
      data: {
        userId,
        verified: true
      }
    });
  } catch (error: any) {
    console.error('Error verifying payment code:', error);
    return res.status(500).json({
      status: false,
      message: 'PAYMENT_VERIFICATION_FAILED',
      meta: {
        error: error.message,
        suggestions: ['Try again later.'],
      },
    });
  }
};


/**
 * Update the 4-digit PIN for the account.
 */
export const updatePin = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const { oldPin, newPin } = req.body;

  // Validate userId
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'The provided userId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid userId and try again.'],
      },
    });
  }

  // Validate oldPin and newPin (must be 4 digits)
  if (!/^\d{4}$/.test(oldPin) || !/^\d{4}$/.test(newPin)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_PIN',
      meta: {
        error: 'Both oldPin and newPin must be 4-digit numbers.',
        suggestions: ['Provide valid 4-digit PINs.'],
      },
    });
  }

  try {
    // Find the account and explicitly select the PIN field
    const account = await Account.findOne({ userId }).select('+pin').exec();

    if (!account) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'No account exists for the given userId.',
          suggestions: ['Check the userId or create an account first.'],
        },
      });
    }

    if (!account.pin) {
      return res.status(400).json({
        status: false,
        message: 'PIN_NOT_SET',
        meta: {
          error: 'No PIN has been set for this account.',
          suggestions: ['Set a PIN first and try again.'],
        },
      });
    }

    // Verify the old PIN
    const isMatch = await bcrypt.compare(oldPin, account.pin);

    if (!isMatch) {
      return res.status(400).json({
        status: false,
        message: 'INCORRECT_OLD_PIN',
        meta: {
          error: 'The provided old PIN is incorrect.',
          suggestions: ['Check the old PIN and try again.'],
        },
      });
    }

    // Hash the new PIN
    const saltRounds = 10;
    const hashedNewPin = await bcrypt.hash(newPin, saltRounds);

    // Update the PIN
    account.pin = hashedNewPin;
    await account.save();

    return res.status(200).json({
      status: true,
      message: 'PIN_UPDATED_SUCCESSFULLY',
      data: {
        userId: account.userId,
        accountNumber: account.accountNumber,
      },
    });
  } catch (error: any) {
    console.error('Error updating PIN:', error);

    return res.status(500).json({
      status: false,
      message: 'PIN_UPDATE_FAILED',
      meta: {
        error: error.message,
        suggestions: [
          'Check the input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};

/**
 * Delete the 4-digit PIN for the account.
 */
export const deletePin = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const { pin } = req.body;

  // Validate userId
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'The provided userId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid userId and try again.'],
      },
    });
  }

  // Validate PIN (must be 4 digits)
  if (!/^\d{4}$/.test(pin)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_PIN',
      meta: {
        error: 'PIN must be a 4-digit number.',
        suggestions: ['Provide a valid 4-digit PIN.'],
      },
    });
  }

  try {
    // Find the account and explicitly select the PIN field
    const account = await Account.findOne({ userId }).select('+pin').exec();

    if (!account) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'No account exists for the given userId.',
          suggestions: ['Check the userId or create an account first.'],
        },
      });
    }

    if (!account.pin) {
      return res.status(400).json({
        status: false,
        message: 'PIN_NOT_SET',
        meta: {
          error: 'No PIN has been set for this account.',
          suggestions: ['Set a PIN first and try again.'],
        },
      });
    }

    // Verify the PIN
    const isMatch = await bcrypt.compare(pin, account.pin);

    if (!isMatch) {
      return res.status(400).json({
        status: false,
        message: 'INCORRECT_PIN',
        meta: {
          error: 'The provided PIN is incorrect.',
          suggestions: ['Check the PIN and try again.'],
        },
      });
    }

    // Delete the PIN
    account.pin = undefined;
    await account.save();

    return res.status(200).json({
      status: true,
      message: 'PIN_DELETED_SUCCESSFULLY',
      data: {
        userId: account.userId,
        accountNumber: account.accountNumber,
      },
    });
  } catch (error: any) {
    console.error('Error deleting PIN:', error);

    return res.status(500).json({
      status: false,
      message: 'PIN_DELETION_FAILED',
      meta: {
        error: error.message,
        suggestions: [
          'Check the input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};



export const addBeneficiary = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const { name, bankName, accountNumber, routingNumber, currency, country, email, phoneNumber } = req.body;

  // Validate userId
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'The provided userId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid userId and try again.'],
      },
    });
  }

  // Validate required fields
  if (!name || !bankName || !accountNumber) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_REQUIRED_FIELDS',
      meta: {
        error: 'Required fields are missing.',
        suggestions: ['Provide all required fields: name, bankName, accountNumber, routingNumber, currency, country.'],
      },
    });
  }

  try {
    // Create a new beneficiary
    const beneficiary = new Beneficiary({
      userId,
      name,
      bankName,
      accountNumber,
      routingNumber,
      currency,
      country,
      email,
      phoneNumber,
    });

    // Save the beneficiary to the database
    await beneficiary.save();

    return res.status(201).json({
      status: true,
      message: 'BENEFICIARY_ADDED_SUCCESSFULLY',
      data: beneficiary,
    });
  } catch (error: any) {
    console.error('Error adding beneficiary:', error);

    return res.status(500).json({
      status: false,
      message: 'BENEFICIARY_ADDITION_FAILED',
      meta: {
        error: error.message,
        suggestions: [
          'Check your input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};

export const getBeneficiaries = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;

  // Validate userId
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'The provided userId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid userId and try again.'],
      },
    });
  }

  try {
    // Find all beneficiaries for the user
    const beneficiaries = await Beneficiary.find({ userId });

    return res.status(200).json({
      status: true,
      message: 'BENEFICIARIES_FETCHED_SUCCESSFULLY',
      data: beneficiaries,
    });
  } catch (error: any) {
    console.error('Error fetching beneficiaries:', error);

    return res.status(500).json({
      status: false,
      message: 'BENEFICIARIES_FETCH_FAILED',
      meta: {
        error: error.message,
        suggestions: [
          'Check your input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};


export const deleteBeneficiary = async (req: Request, res: Response): Promise<any> => {
  const { beneficiaryId } = req.params;

  // Validate beneficiaryId
  if (!beneficiaryId || !mongoose.Types.ObjectId.isValid(beneficiaryId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_BENEFICIARY_ID',
      meta: {
        error: 'The provided beneficiaryId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid beneficiaryId and try again.'],
      },
    });
  }

  try {
    // Delete the beneficiary
    const deletedBeneficiary = await Beneficiary.findByIdAndDelete(beneficiaryId);

    if (!deletedBeneficiary) {
      return res.status(404).json({
        status: false,
        message: 'BENEFICIARY_NOT_FOUND',
        meta: {
          error: 'Beneficiary with the provided ID does not exist.',
          suggestions: ['Check the beneficiary ID and try again.'],
        },
      });
    }

    return res.status(200).json({
      status: true,
      message: 'BENEFICIARY_DELETED_SUCCESSFULLY',
      data: deletedBeneficiary,
    });
  } catch (error: any) {
    console.error('Error deleting beneficiary:', error);

    return res.status(500).json({
      status: false,
      message: 'BENEFICIARY_DELETION_FAILED',
      meta: {
        error: error.message,
        suggestions: [
          'Check your input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};
