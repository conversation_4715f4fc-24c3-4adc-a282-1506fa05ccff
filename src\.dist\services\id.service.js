"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KYCService = void 0;
const faceapi = __importStar(require("face-api.js"));
const tf = __importStar(require("@tensorflow/tfjs-node")); // TensorFlow.js for Node.js
const sharp_1 = __importDefault(require("sharp")); // Sharp for image processing
const path = __importStar(require("path"));
const MODEL_URL = path.join(__dirname, "../aimodels");
class KYCService {
    constructor() {
        this.loadModels();
    }
    async loadModels() {
        await faceapi.nets.faceRecognitionNet.loadFromDisk(MODEL_URL);
        await faceapi.nets.faceLandmark68Net.loadFromDisk(MODEL_URL);
        await faceapi.nets.ssdMobilenetv1.loadFromDisk(MODEL_URL);
    }
    async extractFace(imagePath) {
        const imageBuffer = await (0, sharp_1.default)(imagePath)
            .resize(224, 224)
            .toBuffer();
        const imageTensor = tf.node.decodeImage(imageBuffer);
        const [height, width] = imageTensor.shape;
        const imageData = await tf.browser.toPixels(imageTensor);
        const imageDataObj = new ImageData(new Uint8ClampedArray(imageData), width, height);
        const canvas = document.createElement("canvas");
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext("2d");
        if (!ctx) {
            throw new Error("Could not get canvas context.");
        }
        ctx.putImageData(imageDataObj, 0, 0);
        const detections = await faceapi.detectSingleFace(canvas)
            .withFaceLandmarks()
            .withFaceDescriptor();
        imageTensor.dispose();
        return detections;
    }
    // Compare two faces
    async compareFaces(faceDescriptor1, // Use Float32Array instead of FaceDescriptor
    faceDescriptor2) {
        const distance = faceapi.euclideanDistance(faceDescriptor1, faceDescriptor2);
        return distance < 0.6; // Adjust threshold as needed
    }
    // Verify KYC by comparing multiple selfie images with the picture extracted from the ID card
    async verifyKYC(selfiePaths, idCardImagePath) {
        try {
            const idCardDescriptor = await this.extractFace(idCardImagePath);
            if (!idCardDescriptor) {
                throw new Error("No face detected in the ID card image.");
            }
            // Extract faces from all selfie images
            const selfieDescriptors = await Promise.all([
                this.extractFace(selfiePaths.front),
                this.extractFace(selfiePaths.left),
                this.extractFace(selfiePaths.right),
                this.extractFace(selfiePaths.up),
                this.extractFace(selfiePaths.down),
            ]);
            // Check if faces were detected in all selfie images
            if (selfieDescriptors.some((desc) => !desc)) {
                throw new Error("No face detected in one or more selfie images.");
            }
            // Compare each selfie with the ID card picture
            const comparisons = await Promise.all(selfieDescriptors.map((desc) => this.compareFaces(desc.descriptor, idCardDescriptor.descriptor)));
            return comparisons.every((match) => match);
        }
        catch (error) {
            console.error("Error during KYC verification:", error);
            throw error;
        }
    }
}
exports.KYCService = KYCService;
