"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const mobilemoney_1 = require("../callbacks/mobilemoney");
const callbackRouter = express_1.default.Router();
callbackRouter.post('/mobile-money', mobilemoney_1.mobileMoneyCallback);
exports.default = callbackRouter;
