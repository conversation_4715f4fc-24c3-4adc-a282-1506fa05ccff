"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const developer_controller_1 = require("../controllers/developer.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const devRouter = express_1.default.Router();
devRouter.post('/authorization', developer_controller_1.generateGrantToken);
devRouter.post('/decode', developer_controller_1.decodeToken);
devRouter.post('/access-token', developer_controller_1.generateAccessToken);
devRouter.post('/refresh-token', developer_controller_1.refreshAccessTokenController);
devRouter.get('/user/:userId/credentials', auth_middleware_1.authenticateToken, developer_controller_1.getClientCredentials);
exports.default = devRouter;
