import mongoose, { Schema, Document } from 'mongoose';
import { Transaction } from './transaction.model';
import { IUser } from './user.model';

const SequenceSchema = new Schema({
  name: { type: Schema.Types.Mixed, required: true, unique: true },
  value: { type: Schema.Types.Mixed, default: 0 },
});

export interface ISequence extends Document {
  name: any;
  value: any;
}

const Sequence = mongoose.model<ISequence>('Sequence', SequenceSchema);

const CreditCardSchema = new Schema({
  cardholderId: { type: Schema.Types.Mixed, required: true },
  name: { type: Schema.Types.Mixed, required: true },
  email: { type: Schema.Types.Mixed, required: true },
  phone_number: { type: Schema.Types.Mixed, required: true },
  billing_address: {
    city: { type: Schema.Types.Mixed, required: true },
    country: { type: Schema.Types.Mixed, required: true },
    line1: { type: Schema.Types.Mixed, required: true },
    line2: { type: Schema.Types.Mixed },
    postal_code: { type: Schema.Types.Mixed, required: true },
    state: { type: Schema.Types.Mixed },
  },
  metadata: { type: Schema.Types.Mixed },
  status: { type: Schema.Types.Mixed, required: true },
  created: { type: Schema.Types.Mixed, required: true },
  cardDetails: { type: Schema.Types.Mixed },
});

const AccountSchema = new Schema({
  accountNumber: { type: Schema.Types.Mixed, required: true, unique: true },
  userId: { type: Schema.Types.Mixed, ref: 'User', required: true },
  username: { type: Schema.Types.Mixed },
  account_type: {
    type: Schema.Types.Mixed,
    required: true,
    enum: ['savings', 'current', 'business'],
  },
  balance: [{
    currency: { type: Schema.Types.Mixed },
    amount: { type: Schema.Types.Mixed, default: 0 }, 
    walletBalance: { type: Schema.Types.Mixed, default: 0 }, 
    cardBalance: { type: Schema.Types.Mixed, default: 0 },   
  }],
  countries: {
    type: [Schema.Types.Mixed],
    default: ['USD', 'LRD', 'SLL', 'GNF'],
  },
  status: {
    type: Schema.Types.Mixed,
    default: 'active',
    enum: ['active', 'inactive', 'suspended'],
  },
  transactions: [{ type: Schema.Types.Mixed, ref: 'Transaction' }],
  stripeCardId: { type: Schema.Types.Mixed },
  stripeCardMetadata: { type: Schema.Types.Mixed },
  creditCard: { type: Schema.Types.Mixed },
  pin: { type: String, select: false }, 
  created_at: { type: Schema.Types.Mixed, default: Date.now },
  updated_at: { type: Schema.Types.Mixed, default: Date.now },
});

export interface IAccount extends Document {
  accountNumber: any;
  userId: any;
  username: any;
  account_type: any;
  balance: {
    currency: any;
    amount?: number;
    walletBalance: number;
    cardBalance: number;
  }[];
  countries: any[];
  status: any;
  transactions: any[];
  stripeCardId: any;
  stripeCardMetadata: any;
  creditCard: any;
  pin?: string; 
  created_at: any;
  updated_at: any;
}

const Account = mongoose.model<IAccount>('Account', AccountSchema);

export { Account, Sequence };