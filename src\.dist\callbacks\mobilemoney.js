"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mobileMoneyCallback = void 0;
const mobileMoneyCallback = async (req, res) => {
    const payload = req.body; // The payload sent by the mobile money provider
    console.log("Data from mobile money", payload);
    try {
        return res.status(200).json({
            status: true,
            message: 'CALLBACK_PROCESSED_SUCCESSFULLY',
            data: {
                payload: payload,
            },
        });
    }
    catch (error) {
        console.error('Error processing mobile money callback:', error);
        // Return error response
        return res.status(500).json({
            status: false,
            message: 'CALLBACK_PROCESSING_FAILED',
            meta: {
                error: error.message,
                suggestions: [
                    'Check the payload for errors.',
                    'Ensure the database is running.',
                    'Try again later.',
                ],
            },
        });
    }
};
exports.mobileMoneyCallback = mobileMoneyCallback;
