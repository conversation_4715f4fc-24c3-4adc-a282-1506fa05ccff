"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const mobilemoney_1 = require("../callbacks/mobilemoney");
const billpayment_controller_1 = require("../controllers/billpayment.controller");
const billpaymentRouter = express_1.default.Router();
billpaymentRouter.post('/mobile-money', mobilemoney_1.mobileMoneyCallback);
billpaymentRouter.get('/airtime/operator', billpayment_controller_1.lookupAirtimeOperator);
billpaymentRouter.post('/airtime/purchase', billpayment_controller_1.purchaseAirtime);
// billpaymentRouter.post('/airtime/webhook', handleAirtimeWebhook);
exports.default = billpaymentRouter;
