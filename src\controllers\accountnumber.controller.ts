import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { createAccountService, createTransactionReceipt, generateTransactionId } from '../services/accountnumber.service';
import { Account, IAccount } from '../models/accountnumber.model';
import Queue from 'bull';
import crypto from 'crypto';
import dotenv from 'dotenv';
import validator from 'validator';
import { Mutex } from 'async-mutex';
import { Transaction } from '../models/transaction.model';
import User, { IUser } from '../models/user.model';

dotenv.config();

// Helper function to sanitize and validate input
const sanitizeInput = (input: string): string => {
  return validator.escape(validator.trim(input));
};

const SUPPORTED_ACCOUNT_TYPES = ['savings', 'current', 'business'];
const SUPPORTED_CURRENCIES = ['USD', 'LRD', 'SLL', 'GNF'];


export const createAccountNumber = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;
  const { account_type, currency } = req.body;

  // Validate userId
  if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_USER_ID',
      meta: {
        error: 'The provided userId is missing or not a valid ObjectId.',
        suggestions: ['Provide a valid userId and try again.'],
      },
    });
  }

  // Validate account_type
  if (!account_type || !SUPPORTED_ACCOUNT_TYPES.includes(account_type)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_ACCOUNT_TYPE',
      meta: {
        error: 'The provided account type is missing or not supported.',
        suggestions: [`Provide a valid account type: ${SUPPORTED_ACCOUNT_TYPES.join(', ')}.`],
      },
    });
  }

  // Validate currency
  if (!currency || !SUPPORTED_CURRENCIES.includes(currency)) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_CURRENCY',
      meta: {
        error: 'The provided currency is missing or not supported.',
        suggestions: [`Provide a valid currency: ${SUPPORTED_CURRENCIES.join(', ')}.`],
      },
    });
  }

  try {
    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        status: false,
        message: 'USER_NOT_FOUND',
        meta: {
          error: 'User with the provided ID does not exist.',
          suggestions: ['Check the user ID and try again.'],
        },
      });
    }

    // Check if the user has the appropriate role for the account type
    if (account_type === 'business' && user.role !== 'Business') {
      return res.status(403).json({
        status: false,
        message: 'UNAUTHORIZED_ACCOUNT_CREATION',
        meta: {
          error: 'Only users with the "Business" role can create business accounts.',
          suggestions: ['Upgrade your account to a business account or choose a different account type.'],
        },
      });
    }

    // Check if the user already has an account of the same type
    const existingAccount = await Account.findOne({ userId, account_type });
    if (existingAccount) {
      return res.status(400).json({
        status: false,
        message: 'ACCOUNT_ALREADY_EXISTS',
        meta: {
          error: 'User already has an account of this type.',
          suggestions: ['Provide a different account type or update the existing account.'],
        },
      });
    }

    // Use the service to create the account
    const account = await createAccountService(userId, account_type);

    return res.status(201).json({
      status: true,
      message: 'ACCOUNT_CREATED_SUCCESSFULLY',
      data: account,
    });
  } catch (error: any) {
    console.error('Error during account creation:', error);

    return res.status(500).json({
      status: false,
      message: 'ACCOUNT_CREATION_FAILED',
      meta: {
        error: error.message,
        suggestions: [
          'Check your input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};


export const intraTransfer = async (req: Request, res: Response): Promise<any> => {
  const MAX_RETRIES = 3;
  const RETRY_DELAY_MS = 200;

  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const {
        senderAccountNumber,
        recipientAccountNumber,
        amount,
        customDescription,
        userId,
        currency,
      } = req.body;

      if (
        !senderAccountNumber ||
        !recipientAccountNumber ||
        !amount ||
        amount <= 0 ||
        !userId ||
        !currency
      ) {
        await session.abortTransaction();
        session.endSession();
        return res.status(400).json({
          status: false,
          message: 'INVALID_INPUT',
          meta: { error: 'Missing fields or invalid amount/currency.' },
        });
      }

      if (senderAccountNumber === recipientAccountNumber) {
        await session.abortTransaction();
        session.endSession();
        return res.status(400).json({
          status: false,
          message: 'INVALID_TRANSACTION',
          meta: { error: 'Cannot transfer to same account.' },
        });
      }

      const timestamp = new Date().toISOString().replace(/[-:T.Z]/g, '').slice(0, 14);
      const transactionId = generateTransactionId(`${senderAccountNumber}-${recipientAccountNumber}-${timestamp}`);
      const receipt = createTransactionReceipt('SANGA', 'TRANSFER', timestamp, transactionId);

      const sender = await Account.findOne({ accountNumber: senderAccountNumber })
        .select('+balance +status')
        .session(session);
      const recipient = await Account.findOne({ accountNumber: recipientAccountNumber })
        .select('+balance +status')
        .session(session);

      if (!sender || !recipient) {
        await session.abortTransaction();
        session.endSession();
        return res.status(404).json({
          status: false,
          message: 'ACCOUNT_NOT_FOUND',
        });
      }

      if (sender.userId.toString() !== userId) {
        await session.abortTransaction();
        session.endSession();
        return res.status(403).json({
          status: false,
          message: 'UNAUTHORIZED_ACCESS',
        });
      }

      if (sender.status !== 'active' || recipient.status !== 'active') {
        await session.abortTransaction();
        session.endSession();
        return res.status(403).json({
          status: false,
          message: 'ACCOUNT_INACTIVE',
        });
      }

      const senderUser = await User.findById(sender.userId).session(session);
      const recipientUser = await User.findById(recipient.userId).session(session);
      if (!senderUser || !recipientUser) throw new Error('User not found');

      const senderBalance = sender.balance.find((b: any) => b.currency === currency);
      if (!senderBalance || senderBalance.walletBalance < amount) {
        await session.abortTransaction();
        session.endSession();
        return res.status(400).json({
          status: false,
          message: 'INSUFFICIENT_FUNDS',
        });
      }

      const dailyTransferLimit = 10000;
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const dailyTransfers = await Transaction.find({
        'sender.userId': sender.userId,
        createdAt: { $gte: today },
      }).session(session);

      const dailyTotal = dailyTransfers.reduce((sum, txn) => sum + txn.amount, 0);
      if (dailyTotal + amount > dailyTransferLimit) {
        await session.abortTransaction();
        session.endSession();
        return res.status(400).json({
          status: false,
          message: 'TRANSFER_LIMIT_EXCEEDED',
        });
      }

      // Deduct from sender wallet
      senderBalance.walletBalance -= amount;
      await sender.save({ session });

      // Credit recipient wallet
      let recipientBalance = recipient.balance.find((b: any) => b.currency === currency);
      if (!recipientBalance) {
        recipient.balance.push({
          currency,
          walletBalance: amount,
          cardBalance: 0,
        });
      } else {
        recipientBalance.walletBalance += amount;
      }
      await recipient.save({ session });

      // Save Transaction
      const transaction = new Transaction({
        transactionId: receipt,
        type: 'TRANSFER',
        title: `Transfer from ${senderUser.full_legal_name} to ${recipientUser.full_legal_name}`,
        amount,
        sender: {
          userId: sender.userId,
          name: senderUser.full_legal_name,
          accountNumber: sender.accountNumber,
        },
        recipient: {
          userId: recipient.userId,
          name: recipientUser.full_legal_name,
          accountNumber: recipient.accountNumber,
        },
        customDescription,
        predefinedDescription: `Transferred ${amount} ${currency} from ${senderUser.full_legal_name} to ${recipientUser.full_legal_name}`,
        status: 'SUCCESS',
      });

      await transaction.save({ session });
      await session.commitTransaction();
      session.endSession();

      return res.status(200).json({
        status: true,
        message: 'TRANSACTION_SUCCESS',
        data: {
          receipt,
          amount,
          sender: {
            userId: sender.userId,
            name: senderUser.full_legal_name,
            accountNumber: sender.accountNumber,
            senderBalance: senderBalance.walletBalance,
          },
          recipient: {
            userId: recipient.userId,
            name: recipientUser.full_legal_name,
            accountNumber: recipient.accountNumber,
            recipientBalance: recipient.balance.find((b: any) => b.currency === currency)?.walletBalance || amount,
          },
          timestamp: new Date().toISOString(),
          customDescription,
        },
      });
    } catch (error: any) {
      await session.abortTransaction();
      session.endSession();

      const isTransient =
        error?.errorLabels?.includes('TransientTransactionError') ||
        error?.message?.includes('WriteConflict');

      if (isTransient && attempt < MAX_RETRIES) {
        console.warn(`Retrying intraTransfer: attempt ${attempt} due to transient error:`, error.message);
        await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY_MS * attempt));
        continue;
      }

      console.error(`intraTransfer failed on attempt ${attempt}:`, error);
      return res.status(500).json({
        status: false,
        message: 'TRANSACTION_FAILED',
        meta: {
          error: error.message,
          transactionStatus: 'FAILED',
        },
      });
    }
  }
};



/**
 * Controller function to fetch and return wallet data for a user.
 */
export const getWalletData = async (req: Request, res: Response): Promise<any> => {
  const { userId } = req.params;

  // Validate userId
  if (!userId) {
    return res.status(400).json({
      status: false,
      message: 'MISSING_USER_ID',
      meta: {
        error: 'The userId parameter is required.',
        suggestions: ['Provide a valid userId in the request parameters.'],
      },
    });
  }

  try {
    // Step 1: Fetch the user's account using userId
    const account: IAccount | null = await Account.findOne({ userId }).exec();

    // Step 2: Check if the account exists
    if (!account) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'No account found for the provided userId.',
          suggestions: ['Check the userId and try again.'],
        },
      });
    }

    // Step 3: Prepare the wallet data to return
    const walletData = {
      accountNumber: account.accountNumber,
      accountType: account.account_type,
      balance: account.balance, // Array of balances for different currencies
      countries: account.countries, // Supported countries/currencies
      status: account.status,
      transactions: account.transactions, // List of transactions
      creditCard: account.creditCard, // Credit card details (if available)
      stripeCardId: account.stripeCardId, // Stripe card ID (if available)
      stripeCardMetadata: account.stripeCardMetadata, // Stripe card metadata (if available)
      createdAt: account.created_at,
      updatedAt: account.updated_at,
    };

    // Step 4: Return the wallet data
    return res.status(200).json({
      status: true,
      message: 'WALLET_DATA_RETRIEVED_SUCCESSFULLY',
      data: walletData,
    });
  } catch (error) {
    console.error('Error fetching wallet data:', error);

    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: (error as Error).message,
        suggestions: [
          'Check the input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};


export const aggregateUserTransactions = async (req: Request, res: Response): Promise<any> => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        status: false,
        message: "USER_ID_REQUIRED",
        meta: {
          error: "No user ID provided.",
          suggestions: ["Ensure you are logged in or provide a valid user ID."],
        },
      });
    }

    const allowedTypes = [
      "TRANSFER", "RECEIVED_MONEY", "WITHDRAW", "CREDIT", "DEBIT",
      "CASHBACK", "SWAP", "DEPOSIT", "REFUND", "BILL_PAYMENT", "LOAN_REPAYMENT",
      "INTEREST_EARNED", "FEE_CHARGED", "CARD",
    ];

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const [transactions, totalTransactions, cardTransactions] = await Promise.all([
      // Fetch all transactions (excluding card transactions)
      Transaction.aggregate([
        {
          $match: {
            $and: [
              {
                $or: [
                  { "sender.userId": new mongoose.Types.ObjectId(userId) },
                  { "recipient.userId": new mongoose.Types.ObjectId(userId) },
                ],
              },
              { type: { $in: allowedTypes, $ne: "CARD" } }, // Exclude card transactions
            ],
          },
        },
        { $sort: { timestamp: -1 } },
        { $skip: skip },
        { $limit: limit },
        {
          $project: {
            _id: 0, // Exclude the MongoDB _id field
            id: "$transactionId",
            type: "$type",
            title: {
              $cond: [
                { $eq: ["$type", "SWAP"] }, // Check if the transaction type is SWAP
                "$predefinedDescription", // Use the predefinedDescription for SWAP transactions
                {
                  $cond: [
                    { $eq: ["$sender.userId", new mongoose.Types.ObjectId(userId)] },
                    { $concat: ["Transfer to ", "$recipient.name"] },
                    { $concat: ["Transfer from ", "$sender.name"] },
                  ],
                },
              ],
            },
            amount: {
              value: { $abs: "$amount" },
              currency: "USD",
              direction: {
                $cond: [
                  { $eq: ["$sender.userId", new mongoose.Types.ObjectId(userId)] },
                  "outgoing",
                  "incoming",
                ],
              },
            },
            counterparty: {
              $cond: [
                { $eq: ["$sender.userId", new mongoose.Types.ObjectId(userId)] },
                "$recipient",
                "$sender",
              ],
            },
            description: {
              custom: "$customDescription",
              system: "$predefinedDescription", // Include the system description
            },
            status: 1,
            transaction_date: "$timestamp",
            metadata: {
              sender_account: "$sender.accountNumber",
              recipient_account: "$recipient.accountNumber",
            },
          },
        },
      ]),

      // Count total transactions (excluding card transactions)
      Transaction.countDocuments({
        $or: [
          { "sender.userId": new mongoose.Types.ObjectId(userId) },
          { "recipient.userId": new mongoose.Types.ObjectId(userId) },
        ],
        type: { $in: allowedTypes, $ne: "CARD" }, // Exclude card transactions
      }),

      // Fetch card transactions separately
      Transaction.aggregate([
        {
          $match: {
            $and: [
              {
                $or: [
                  { "sender.userId": new mongoose.Types.ObjectId(userId) },
                  { "recipient.userId": new mongoose.Types.ObjectId(userId) },
                ],
              },
              { type: "CARD" }, // Filter only card transactions
            ],
          },
        },
        { $sort: { timestamp: -1 } },
        { $skip: skip },
        { $limit: limit },
        {
          $project: {
            _id: 0, // Exclude the MongoDB _id field
            transactionId: 1, // Include the transactionId field
            type: 1, // Include the type field
            title: 1, // Include the title field
            amount: 1, // Include the amount field
            sender: 1, // Include the sender field
            predefinedDescription: 1, // Include the predefinedDescription field
            status: 1, // Include the status field
            timestamp: 1, // Include the timestamp field
            cardTransaction: 1, // Include the cardTransaction field
            receipt: 1, // Include the receipt field
          },
        },
      ]),
    ]);

    const hasMore = totalTransactions > page * limit;
    const baseUrl = `${req.protocol}://${req.get("host")}${req.baseUrl}${req.path}`;
    const nextPageUrl = hasMore ? `${baseUrl}?page=${page + 1}&limit=${limit}` : null;

    return res.status(200).json({
      status: true,
      message: "TRANSACTIONS_FETCHED_SUCCESSFULLY",
      data: {
        transactions, // All transactions (excluding card transactions)
        cardTransactions, // Only card transactions
        total: totalTransactions,
        pagination: {
          has_more: hasMore,
          page,
          per_page: limit,
          next_page_url: nextPageUrl,
        },
      },
    });
  } catch (error: any) {
    console.error("Error fetching transactions:", error);
    return res.status(500).json({
      status: false,
      message: "TRANSACTION_FETCH_FAILED",
      meta: {
        error: error.message,
        suggestions: [
          "Check your input data.",
          "Ensure the database is running.",
          "Try again later.",
        ],
      },
    });
  }
};


export const getUserInfoByAccountNumber = async (req: Request, res: Response): Promise<any> => {
  const { accountNumber } = req.params;

  // Validate accountNumber
  if (!accountNumber) {
    return res.status(400).json({
      status: false,
      message: 'INVALID_INPUT',
      meta: {
        error: 'The provided accountNumber is missing or not valid.',
        suggestions: ['Provide a valid accountNumber and try again.'],
      },
    });
  }

  try {
    // Find the account by account number
    const account = await Account.findOne({ accountNumber });
    if (!account) {
      return res.status(404).json({
        status: false,
        message: 'ACCOUNT_NOT_FOUND',
        meta: {
          error: 'Account with the provided account number does not exist.',
          suggestions: ['Check the account number and try again.'],
        },
      });
    }

    // Find the user by user ID from the account
    const user = await User.findById(account.userId);
    if (!user) {
      return res.status(404).json({
        status: false,
        message: 'USER_NOT_FOUND',
        meta: {
          error: 'User associated with the provided account number does not exist.',
          suggestions: ['Check the account number and try again.'],
        },
      });
    }

    // Return user information
    return res.status(200).json({
      status: true,
      message: 'USER_INFO_RETRIEVED_SUCCESSFULLY',
      data: {
        userId: user._id, // Added user ID to the response
        accountNumber: account.accountNumber,
        fullName: user.full_legal_name,
      },
    });
  } catch (error: any) {
    console.error('Error fetching user info:', error);

    return res.status(500).json({
      status: false,
      message: 'INTERNAL_SERVER_ERROR',
      meta: {
        error: error.message,
        suggestions: [
          'Check your input data for errors.',
          'Ensure the database is running.',
          'Try again later.',
        ],
      },
    });
  }
};
