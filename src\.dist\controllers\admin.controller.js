"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.upgradeAccountToBusiness = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const user_model_1 = __importDefault(require("../models/user.model"));
/**
 * Add balance to a user's wallet.
 */
// export const addBalanceToWallet = async (req: Request, res: Response): Promise<any> => {
//   const { userId } = req.params;
//   const { currency, amount } = req.body;
//   // Validate userId
//   if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
//     return res.status(400).json({
//       status: false,
//       message: 'INVALID_USER_ID',
//       meta: {
//         error: 'The provided userId is missing or not a valid ObjectId.',
//         suggestions: ['Provide a valid userId and try again.'],
//       },
//     });
//   }
//   // Validate currency and amount
//   if (!currency || typeof amount !== 'number' || amount <= 0) {
//     return res.status(400).json({
//       status: false,
//       message: 'INVALID_AMOUNT_OR_CURRENCY',
//       meta: {
//         error: 'Currency is required, and amount must be a positive number.',
//         suggestions: ['Provide a valid currency and amount.'],
//       },
//     });
//   }
//   try {
//     // Find the account
//     const account = await Account.findOne({ userId });
//     if (!account) {
//       return res.status(404).json({
//         status: false,
//         message: 'ACCOUNT_NOT_FOUND',
//         meta: {
//           error: 'No account exists for the given userId.',
//           suggestions: ['Check the userId or create an account first.'],
//         },
//       });
//     }
//     // Check if the currency exists in the balance array
//     const balanceIndex = account.balance.findIndex((b: any) => b.currency === currency);
//     if (balanceIndex !== -1) {
//       // Update existing walletBalance
//       account.balance[balanceIndex].walletBalance += amount;
//     } else {
//       // Initialize new currency entry
//       account.balance.push({
//         currency,
//         walletBalance: amount,
//         cardBalance: 0,
//       });
//     }
//     // Save updated account
//     await account.save();
//     return res.status(200).json({
//       status: true,
//       message: 'BALANCE_ADDED_SUCCESSFULLY',
//       data: {
//         userId: account.userId,
//         accountNumber: account.accountNumber,
//         balance: account.balance,
//       },
//     });
//   } catch (error: any) {
//     console.error('Error adding balance:', error);
//     return res.status(500).json({
//       status: false,
//       message: 'BALANCE_ADDITION_FAILED',
//       meta: {
//         error: error.message,
//         suggestions: [
//           'Check the input data for errors.',
//           'Ensure the database is running.',
//           'Try again later.',
//         ],
//       },
//     });
//   }
// };
const upgradeAccountToBusiness = async (req, res) => {
    const { userId } = req.params;
    // Validate userId
    if (!userId || !mongoose_1.default.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({
            status: false,
            message: 'INVALID_USER_ID',
            meta: {
                error: 'The provided userId is missing or not a valid ObjectId.',
                suggestions: ['Provide a valid userId and try again.'],
            },
        });
    }
    try {
        // Find the user by userId
        const user = await user_model_1.default.findById(userId).exec();
        // Check if the user exists
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'USER_NOT_FOUND',
                meta: {
                    error: 'No user found with the provided userId.',
                    suggestions: ['Verify the userId and try again.'],
                },
            });
        }
        // Check if the user is already a business
        if (user.accountType === 'business') {
            return res.status(400).json({
                status: false,
                message: 'ALREADY_BUSINESS_ACCOUNT',
                meta: {
                    error: 'The user is already a business account.',
                    suggestions: ['No action required.'],
                },
            });
        }
        // Validate if the user has uploaded the required KYC documents for business
        const requiredBusinessDocuments = [
            'business_registration',
            'proof_of_business',
            'tax_document',
        ];
        const hasRequiredDocuments = requiredBusinessDocuments.every((docType) => {
            return user.kyc.business[docType]?.length > 0; // Check if at least one document of each type is uploaded
        });
        if (!hasRequiredDocuments) {
            return res.status(400).json({
                status: false,
                message: 'MISSING_REQUIRED_DOCUMENTS',
                meta: {
                    error: 'The user has not uploaded all required KYC documents for a business account.',
                    suggestions: [
                        'Upload the following documents: business_registration, proof_of_business, tax_document.',
                    ],
                },
            });
        }
        // Update the user's account type to business
        user.accountType = 'business';
        // Save the updated user
        await user.save();
        // Return the updated user profile
        return res.status(200).json({
            status: true,
            message: 'ACCOUNT_TYPE_UPDATED_TO_BUSINESS_SUCCESSFULLY',
            data: {
                id: user._id,
                accountType: user.accountType,
                kyc_status: user.kyc_status,
                documents: user.documents,
            },
        });
    }
    catch (error) {
        console.error('Error updating account type to business:', error);
        return res.status(500).json({
            status: false,
            message: 'ACCOUNT_TYPE_UPDATE_FAILED',
            meta: {
                error: error.message,
                suggestions: [
                    'Check the input data for errors.',
                    'Ensure the database is running.',
                    'Try again later.',
                ],
            },
        });
    }
};
exports.upgradeAccountToBusiness = upgradeAccountToBusiness;
