"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Sequence = exports.Account = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const SequenceSchema = new mongoose_1.Schema({
    name: { type: mongoose_1.Schema.Types.Mixed, required: true, unique: true },
    value: { type: mongoose_1.Schema.Types.Mixed, default: 0 },
});
const Sequence = mongoose_1.default.model('Sequence', SequenceSchema);
exports.Sequence = Sequence;
const CreditCardSchema = new mongoose_1.Schema({
    cardholderId: { type: mongoose_1.Schema.Types.Mixed, required: true },
    name: { type: mongoose_1.Schema.Types.Mixed, required: true },
    email: { type: mongoose_1.Schema.Types.Mixed, required: true },
    phone_number: { type: mongoose_1.Schema.Types.Mixed, required: true },
    billing_address: {
        city: { type: mongoose_1.Schema.Types.Mixed, required: true },
        country: { type: mongoose_1.Schema.Types.Mixed, required: true },
        line1: { type: mongoose_1.Schema.Types.Mixed, required: true },
        line2: { type: mongoose_1.Schema.Types.Mixed },
        postal_code: { type: mongoose_1.Schema.Types.Mixed, required: true },
        state: { type: mongoose_1.Schema.Types.Mixed },
    },
    metadata: { type: mongoose_1.Schema.Types.Mixed },
    status: { type: mongoose_1.Schema.Types.Mixed, required: true },
    created: { type: mongoose_1.Schema.Types.Mixed, required: true },
    cardDetails: { type: mongoose_1.Schema.Types.Mixed },
});
const AccountSchema = new mongoose_1.Schema({
    accountNumber: { type: mongoose_1.Schema.Types.Mixed, required: true, unique: true },
    userId: { type: mongoose_1.Schema.Types.Mixed, ref: 'User', required: true },
    username: { type: mongoose_1.Schema.Types.Mixed },
    account_type: {
        type: mongoose_1.Schema.Types.Mixed,
        required: true,
        enum: ['savings', 'current', 'business'],
    },
    balance: [{
            currency: { type: mongoose_1.Schema.Types.Mixed },
            amount: { type: mongoose_1.Schema.Types.Mixed, default: 0 },
            walletBalance: { type: mongoose_1.Schema.Types.Mixed, default: 0 },
            cardBalance: { type: mongoose_1.Schema.Types.Mixed, default: 0 },
        }],
    countries: {
        type: [mongoose_1.Schema.Types.Mixed],
        default: ['USD', 'LRD', 'SLL', 'GNF'],
    },
    status: {
        type: mongoose_1.Schema.Types.Mixed,
        default: 'active',
        enum: ['active', 'inactive', 'suspended'],
    },
    transactions: [{ type: mongoose_1.Schema.Types.Mixed, ref: 'Transaction' }],
    stripeCardId: { type: mongoose_1.Schema.Types.Mixed },
    stripeCardMetadata: { type: mongoose_1.Schema.Types.Mixed },
    creditCard: { type: mongoose_1.Schema.Types.Mixed },
    pin: { type: String, select: false },
    created_at: { type: mongoose_1.Schema.Types.Mixed, default: Date.now },
    updated_at: { type: mongoose_1.Schema.Types.Mixed, default: Date.now },
});
const Account = mongoose_1.default.model('Account', AccountSchema);
exports.Account = Account;
