{"name": "sangapay", "version": "1.0.0", "main": "server.ts", "scripts": {"build": "tsc", "start": "nodemon src/.dist/server.js", "dev": "nodemon"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "description": "", "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/csurf": "^1.11.5", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/node": "^22.13.10", "@types/node-cron": "^3.0.11", "@types/validator": "^13.12.2", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "dependencies": {"@stripe/stripe-js": "^5.5.0", "@tensorflow/tfjs-node": "^4.22.0", "async-mutex": "^0.5.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bee-queue": "^1.7.1", "bull": "^4.16.5", "cloudinary": "^2.5.1", "cloudinary-build-url": "^0.2.4", "cors": "^2.8.5", "crypto": "^1.0.1", "csrf-csrf": "^3.1.0", "csurf": "^1.10.0", "dotenv": "^16.4.7", "express": "^4.21.2", "face-api.js": "^0.20.0", "flutterwave-node-v3": "^1.1.14", "helmet": "^8.0.0", "http": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.5", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "redis": "^4.7.0", "sangapay": "file:", "sharp": "^0.33.5", "stripe": "^17.5.0", "twilio": "^5.4.0", "validator": "^13.12.0", "workerpool": "^9.2.0"}}