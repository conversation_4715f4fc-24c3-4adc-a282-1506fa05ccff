import { subtle } from "crypto";


export async function verifySignatureWithKey(publicKey: CryptoKey, data: string, signature: string): Promise<boolean> {
    try {
      const encoder = new TextEncoder();
      const encodedData = encoder.encode(data);
      const signatureBuffer = Uint8Array.from(Buffer.from(signature, "base64"));
  
      const isValid = await subtle.verify(
        { name: "ECDSA", hash: { name: "SHA-256" } },
        publicKey,
        signatureBuffer,
        encodedData
      );
  
      return isValid;
    } catch (error) {
      console.error("Error verifying signature:", error);
      return false;
    }
  }
  

  

export async function signData(privateKey: CryptoKey, data: string): Promise<string> {
    const encoder = new TextEncoder();
    const encodedData = encoder.encode(data);
    const signature = await subtle.sign(
      { name: "ECDS<PERSON>", hash: { name: "SHA-256" } },
      privateKey,
      encodedData
    );
  
    return Buffer.from(signature).toString("base64");
  }
  