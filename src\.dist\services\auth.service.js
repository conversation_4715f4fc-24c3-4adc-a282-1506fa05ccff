"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifySignatureWithKey = verifySignatureWithKey;
exports.signData = signData;
const crypto_1 = require("crypto");
/**
 * Verify a signature using ECDSA public key
 */
async function verifySignatureWithKey(publicKey, data, signature) {
    try {
        const encoder = new TextEncoder();
        const encodedData = encoder.encode(data);
        const signatureBuffer = Uint8Array.from(Buffer.from(signature, "base64"));
        const isValid = await crypto_1.subtle.verify({ name: "ECDSA", hash: { name: "SHA-256" } }, public<PERSON>ey, signatureBuffer, encodedData);
        return isValid;
    }
    catch (error) {
        console.error("Error verifying signature:", error);
        return false;
    }
}
/**
* Sign data using ECDSA private key
*/
async function signData(privateKey, data) {
    const encoder = new TextEncoder();
    const encodedData = encoder.encode(data);
    const signature = await crypto_1.subtle.sign({ name: "ECDSA", hash: { name: "SHA-256" } }, privateKey, encodedData);
    return Buffer.from(signature).toString("base64");
}
