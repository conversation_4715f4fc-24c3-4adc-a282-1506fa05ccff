// import { Request, Response } from "express";
// import { KYCService } from "../services/id.service";

// export const verifyKYC = async (req: Request, res: Response): Promise<any> => {
//   const { userId } = req.params;
//   const { selfiePaths, idCardImagePath } = req.body;

//   // Validate userId
//   if (!userId) {
//     return res.status(400).json({
//       status: false,
//       message: "INVALID_USER_ID",
//       meta: {
//         error: "The provided userId is missing.",
//         suggestions: ["Provide a valid userId and try again."],
//       },
//     });
//   }

//   // Validate selfiePaths
//   if (
//     !selfiePaths ||
//     !selfiePaths.front ||
//     !selfiePaths.left ||
//     !selfiePaths.right ||
//     !selfiePaths.up ||
//     !selfiePaths.down
//   ) {
//     return res.status(400).json({
//       status: false,
//       message: "INVALID_SELFIE_PATHS",
//       meta: {
//         error: "The provided selfie paths are missing or incomplete.",
//         suggestions: [
//           "Provide all required selfie paths: front, left, right, up, down.",
//         ],
//       },
//     });
//   }

//   // Validate idCardImagePath
//   if (!idCardImagePath) {
//     return res.status(400).json({
//       status: false,
//       message: "INVALID_ID_CARD_IMAGE_PATH",
//       meta: {
//         error: "The provided ID card image path is missing.",
//         suggestions: ["Provide a valid ID card image path and try again."],
//       },
//     });
//   }

//   try {
//     const kycService = new KYCService();

//     const isVerified = await kycService.verifyKYC(selfiePaths, idCardImagePath);

//     return res.status(200).json({
//       status: true,
//       message: "KYC_VERIFICATION_SUCCESSFUL",
//       data: {
//         isVerified,
//       },
//     });
//   } catch (error: any) {
//     console.error("Error during KYC verification:", error);

//     return res.status(500).json({
//       status: false,
//       message: "KYC_VERIFICATION_FAILED",
//       meta: {
//         error: error.message,
//         suggestions: [
//           "Check your input data for errors.",
//           "Ensure the images are valid and contain faces.",
//           "Try again later.",
//         ],
//       },
//     });
//   }
// };