"use strict";
// import <PERSON> from 'bee-queue';
// import mongoose from 'mongoose';
// import { Transaction } from '../models/transaction.model';
// import { Account } from '../models/accountnumber.model';
// // Configure Bee Queue (In-Memory)
// const transactionQueue = new Bee('transactions', {
//   isWorker: true,
//   removeOnSuccess: true,
// });
// // Job Processing Logic
// transactionQueue.process(async (job) => {
//   const {
//     senderUserId,
//     senderAccountNumber,
//     recipientAccountNumber,
//     amount,
//     customDescription,
//     senderTransactionId,
//     recipientTransactionId,
//     retryCount = 0, // Initialize retryCount
//   } = job.data;
//   const session = await mongoose.startSession();
//   session.startTransaction();
//   try {
//     // Fetch Accounts with Pessimistic Locking
//     const senderAccount = await Account.findOne({ accountNumber: senderAccountNumber })
//       .session(session)
//       .select('+balance');
//     const recipientAccount = await Account.findOne({ accountNumber: recipientAccountNumber })
//       .session(session)
//       .select('+balance');
//     if (!senderAccount || !recipientAccount) {
//       throw new Error('Account not found');
//     }
//     if (senderAccount.balance < amount) {
//       throw new Error('Insufficient balance');
//     }
//     // Update Balances
//     senderAccount.balance -= amount;
//     recipientAccount.balance += amount;
//     await Promise.all([
//       senderAccount.save({ session }),
//       recipientAccount.save({ session }),
//     ]);
//     // Create Transactions
//     const transactions = await Transaction.create(
//       [
//         {
//           transactionId: senderTransactionId,
//           type: 'DEBIT',
//           amount: -amount,
//           account: senderAccount._id,
//           metadata: { customDescription },
//         },
//         {
//           transactionId: recipientTransactionId,
//           type: 'CREDIT',
//           amount,
//           account: recipientAccount._id,
//           metadata: { customDescription },
//         },
//       ],
//       { session }
//     );
//     await session.commitTransaction();
//     return transactions;
//   } catch (error: any) {
//     await session.abortTransaction();
//     throw error;
//   } finally {
//     session.endSession();
//   }
// });
// // Handle failed jobs with retries
// transactionQueue.on('failed', async (job, error) => {
//   const retryCount = job.data.retryCount || 0;
//   if (retryCount < 3) {
//     // Re-enqueue the job with delay and retry options
//     await (transactionQueue.createJob as any)(
//       {
//         ...job.data,
//         retryCount: retryCount + 1, // Increment retry count
//       },
//       {
//         retries: 3, // Max retries allowed
//         delay: 1000, // 1-second delay
//       }
//     ).save();
//     console.log(`Retrying job ${job.id} (attempt ${retryCount + 1})`);
//   } else {
//     console.error(`Job ${job.id} failed after 3 retries:`, error);
//   }
// });
// export { transactionQueue };
