import express, { Request, Response } from 'express';
import { Account } from '../models/accountnumber.model';

export const getAllUSStates = async (req: Request, res: Response): Promise<any> => {
  try {
    const usStates = [
      { name: 'Alabama', code: 'AL' },
      { name: 'Alaska', code: 'AK' },
      { name: 'Arizona', code: 'AZ' },
      { name: 'Arkansas', code: 'AR' },
      { name: 'California', code: 'CA' },
      { name: 'Colorado', code: 'CO' },
      { name: 'Connecticut', code: 'CT' },
      { name: 'Delaware', code: 'DE' },
      { name: 'Florida', code: 'FL' },
      { name: 'Georgia', code: 'GA' },
      { name: 'Hawaii', code: 'HI' },
      { name: 'Idaho', code: 'ID' },
      { name: 'Illinois', code: 'IL' },
      { name: 'Indiana', code: 'IN' },
      { name: 'Iowa', code: 'IA' },
      { name: 'Kansas', code: 'KS' },
      { name: 'Kentucky', code: 'KY' },
      { name: 'Louisiana', code: 'LA' },
      { name: 'Maine', code: 'ME' },
      { name: 'Maryland', code: 'MD' },
      { name: 'Massachusetts', code: 'MA' },
      { name: 'Michigan', code: 'MI' },
      { name: 'Minnesota', code: 'MN' },
      { name: 'Mississippi', code: 'MS' },
      { name: 'Missouri', code: 'MO' },
      { name: 'Montana', code: 'MT' },
      { name: 'Nebraska', code: 'NE' },
      { name: 'Nevada', code: 'NV' },
      { name: 'New Hampshire', code: 'NH' },
      { name: 'New Jersey', code: 'NJ' },
      { name: 'New Mexico', code: 'NM' },
      { name: 'New York', code: 'NY' },
      { name: 'North Carolina', code: 'NC' },
      { name: 'North Dakota', code: 'ND' },
      { name: 'Ohio', code: 'OH' },
      { name: 'Oklahoma', code: 'OK' },
      { name: 'Oregon', code: 'OR' },
      { name: 'Pennsylvania', code: 'PA' },
      { name: 'Rhode Island', code: 'RI' },
      { name: 'South Carolina', code: 'SC' },
      { name: 'South Dakota', code: 'SD' },
      { name: 'Tennessee', code: 'TN' },
      { name: 'Texas', code: 'TX' },
      { name: 'Utah', code: 'UT' },
      { name: 'Vermont', code: 'VT' },
      { name: 'Virginia', code: 'VA' },
      { name: 'Washington', code: 'WA' },
      { name: 'West Virginia', code: 'WV' },
      { name: 'Wisconsin', code: 'WI' },
      { name: 'Wyoming', code: 'WY' },
    ];

    res.status(200).json({
      status: true,
      message: 'STATES_FETCHED_SUCCESSFULLY',
      data: usStates,
    });
  } catch (error) {
    console.error('Error fetching US states:', error);
    return res.status(500).json({
      status: false,
      message: 'FAILED_TO_FETCH_STATES',
      meta: {
        error: (error as Error).message,
        suggestions: [
          'Check the server logs for more details.',
          'Try again later.',
        ],
      },
    });
  }
};




export const migrateBalancesToWallet = async (): Promise<void> => {
  try {
    const accounts = await Account.find({});
    let updatedCount = 0;

    for (const account of accounts) {
      let modified = false;

      const updatedBalances = account.balance.map((bal: any) => {
        const update = { ...bal };

        // Migrate legacy 'amount' into walletBalance if needed
        if (typeof update.amount === 'number') {
          if (typeof update.walletBalance !== 'number' || update.walletBalance === 0) {
            update.walletBalance = update.amount;
            modified = true;
          }
        }

        // Ensure cardBalance exists
        if (typeof update.cardBalance !== 'number') {
          update.cardBalance = 0;
          modified = true;
        }

        return update;
      });

      if (modified) {
        account.balance = updatedBalances;
        await account.save();
        updatedCount++;
      }
    }

    console.log(`✅ ACCOUNT_BALANCE_MIGRATION_SUCCESSFUL - Total Updated: ${updatedCount}`);
  } catch (error) {
    console.error('❌ ACCOUNT_BALANCE_MIGRATION_FAILED:', (error as Error).message);
  }
};


export const findMalformedBalances = async (): Promise<void> => {
  const accounts = await Account.find({});
  let malformedCount = 0;

  for (const account of accounts) {
    account.balance.forEach((bal: any, index: number) => {
      if (!bal.currency || typeof bal.currency !== 'string') {
        console.warn(
          `🚨 Malformed balance detected at index ${index} in Account ID ${account._id}:`,
          JSON.stringify(bal, null, 2)
        );
        malformedCount++;
      }
    });
  }

  console.log(`🔍 Total malformed balances found: ${malformedCount}`);
};
