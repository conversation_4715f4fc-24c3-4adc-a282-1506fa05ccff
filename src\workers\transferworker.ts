// import { parentPort, workerData } from 'worker_threads';
// import mongoose from 'mongoose';
// import { Transaction } from '../models/transaction.model';
// import { Account } from '../models/accountnumber.model';
// import User from '../models/user.model';
// import { createTransactionReceipt, generateTransactionId } from '../services/accountnumber.service';

// const { senderAccountNumber, recipientAccountNumber, amount, customDescription, userId } = workerData;

// (async () => {
//   try {
//     // Ensure DB connection (optional, but strongly recommended in worker context)
//     if (mongoose.connection.readyState === 0) {
//       await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/sangapay');
//     }

//     // Generate transaction ID and receipt
//     const timestamp = new Date().toISOString().replace(/[-:T.Z]/g, '').slice(0, 14);
//     const transactionId = generateTransactionId(`${senderAccountNumber}-${recipientAccountNumber}-${timestamp}`);
//     const receipt = createTransactionReceipt('SANGA', 'TRANSFER', timestamp, transactionId);

//     // Find sender and recipient accounts
//     const sender = await Account.findOne({ accountNumber: senderAccountNumber }).select('+balance +status');
//     const recipient = await Account.findOne({ accountNumber: recipientAccountNumber }).select('+balance +status');

//     if (!sender || !recipient) {
//       throw new Error('ACCOUNT_NOT_FOUND: Sender or recipient account not found.');
//     }

//     if (sender.userId.toString() !== userId) {
//       throw new Error('UNAUTHORIZED_ACCESS: You do not have permission to perform this transaction.');
//     }

//     if (sender.status !== 'active' || recipient.status !== 'active') {
//       throw new Error('ACCOUNT_INACTIVE: One or both accounts are not active.');
//     }

//     const senderUser = await User.findById(sender.userId);
//     const recipientUser = await User.findById(recipient.userId);
//     if (!senderUser || !recipientUser) throw new Error('User not found');

//     const senderBalance = sender.balance.find((b: any) => b.currency === 'USD');
//     if (!senderBalance || senderBalance.amount < amount) {
//       throw new Error('INSUFFICIENT_FUNDS: Your balance is too low for this transaction.');
//     }

//     const dailyTransferLimit = 10000;
//     const today = new Date();
//     today.setHours(0, 0, 0, 0);

//     const dailyTransfers = await Transaction.find({
//       'sender.userId': sender.userId,
//       createdAt: { $gte: today },
//     });

//     const dailyTotal = dailyTransfers.reduce((sum, txn) => sum + txn.amount, 0);
//     if (dailyTotal + amount > dailyTransferLimit) {
//       throw new Error(`TRANSFER_LIMIT_EXCEEDED: You have exceeded the daily transfer limit of $${dailyTransferLimit}.`);
//     }

//     // Debit sender
//     senderBalance.amount -= amount;
//     await sender.save();

//     // Credit recipient
//     const recipientBalance = recipient.balance.find((b: any) => b.currency === 'USD');
//     if (!recipientBalance) {
//       recipient.balance.push({ currency: 'USD', amount });
//     } else {
//       recipientBalance.amount += amount;
//     }
//     await recipient.save();

//     // Save transaction
//     const transaction = new Transaction({
//       transactionId: receipt,
//       type: 'TRANSFER',
//       title: `Transfer from ${senderUser.full_legal_name} to ${recipientUser.full_legal_name}`,
//       amount: amount,
//       sender: {
//         userId: sender.userId,
//         name: senderUser.full_legal_name,
//         accountNumber: sender.accountNumber,
//       },
//       recipient: {
//         userId: recipient.userId,
//         name: recipientUser.full_legal_name,
//         accountNumber: recipient.accountNumber,
//       },
//       customDescription,
//       predefinedDescription: `Transferred ${amount} USD from ${senderUser.full_legal_name} to ${recipientUser.full_legal_name}`,
//       status: 'SUCCESS',
//     });

//     await transaction.save();

//     parentPort?.postMessage({
//       status: true,
//       message: 'TRANSACTION_SUCCESS',
//       data: {
//         receipt: transaction.transactionId,
//         sender: {
//           userId: sender.userId,
//           name: senderUser.full_legal_name,
//           accountNumber: sender.accountNumber,
//           senderBalance: senderBalance.amount,
//         },
//         recipient: {
//           userId: recipient.userId,
//           name: recipientUser.full_legal_name,
//           accountNumber: recipient.accountNumber,
//           recipientBalance: recipientBalance?.amount || amount,
//         },
//         timestamp: new Date().toISOString(),
//         customDescription,
//         transactionStatus: 'SUCCESS',
//       },
//     });
//   } catch (error: any) {
//     console.error('Transaction failed:', error);
//     parentPort?.postMessage({
//       status: false,
//       message: 'TRANSACTION_FAILED',
//       meta: {
//         error: error.message,
//         suggestions: ['Verify account balances', 'Check network connection', 'Contact support if issue persists'],
//         transactionStatus: 'FAILED',
//       },
//     });
//   }
// })();
