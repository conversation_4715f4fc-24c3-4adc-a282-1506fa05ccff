"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const helper_controller_1 = require("../controllers/helper.controller");
const exchange_controller_1 = require("../controllers/exchange.controller");
const helperRouter = express_1.default.Router();
helperRouter.get('/fetch-usa-states', helper_controller_1.getAllUSStates);
helperRouter.get('/conversion-rates', exchange_controller_1.getLatestRates);
exports.default = helperRouter;
