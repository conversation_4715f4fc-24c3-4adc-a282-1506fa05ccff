// Decrepated use the new version below 


// import axios from 'axios';
// import dotenv from 'dotenv';
// import { Account } from '../models/accountnumber.model';
// import mongoose from 'mongoose';
// import { createTransactionReceipt, generateTransactionId } from './accountnumber.service';
// import { Transaction } from '../models/transaction.model';

// dotenv.config();

// export class CurrencyConversionService {
//   private readonly apiKey: string;
//   private readonly baseUrl: string;
//   private readonly hardcodedSymbols: string[] = ['NGN', 'SLL', 'LRD', 'GNF']; // Hardcoded currencies

//   constructor() {
//     this.apiKey = process.env.OPEN_EXCHANGE_RATES_API_KEY!;
//     this.baseUrl = 'https://openexchangerates.org/api';
//   }

//   /**
//    * Get the latest exchange rates from USD to specified currencies.
//    * @param symbols - Comma-separated list of currency codes to fetch rates for (e.g., 'GBP,JPY,EUR').
//    * @param includeAllRates - Optional flag to include all rates in the response.
//    */
//   async getLatestRatesFromUSD(
//     symbols: string,
//     includeAllRates: boolean = false
//   ): Promise<{
//     success: boolean;
//     timestamp: number;
//     base: string;
//     date: string;
//     importantRates: { [key: string]: number };
//     allRates?: { [key: string]: number }; // Optional field for all rates
//   }> {
//     try {
//       const params: any = {
//         app_id: this.apiKey, // Use app_id instead of access_key
//         base: 'USD', // Set base currency to USD
//       };

//       // Make the API request to the /latest endpoint
//       const response = await axios.get(`${this.baseUrl}/latest.json`, { params });

//       if (!response.data || !response.data.rates) {
//         throw new Error('Failed to fetch latest rates');
//       }

//       // Extract important rates
//       const importantRates: { [key: string]: number } = {};
//       const symbolList = symbols.split(',');
//       for (const symbol of symbolList) {
//         if (response.data.rates[symbol]) {
//           importantRates[symbol] = response.data.rates[symbol];
//         }
//       }

//       // Prepare the response
//       const result: {
//         success: boolean;
//         timestamp: number;
//         base: string;
//         date: string;
//         importantRates: { [key: string]: number };
//         allRates?: { [key: string]: number };
//       } = {
//         success: true,
//         timestamp: response.data.timestamp,
//         base: response.data.base,
//         date: new Date(response.data.timestamp * 1000).toISOString().split('T')[0], // Convert timestamp to date
//         importantRates,
//       };

//       // Include all rates if requested
//       if (includeAllRates) {
//         result.allRates = response.data.rates;
//       }

//       return result;
//     } catch (error) {
//       console.error('Error fetching latest rates:', error);
//       throw new Error('Failed to fetch latest rates');
//     }
//   }

//   /**
//    * Get the exchange rate between two currencies.
//    * @param base - The base currency (e.g., 'NGN').
//    * @param target - The target currency (e.g., 'SLL').
//    */
//   async getExchangeRate(base: string, target: string): Promise<number> {
//     try {
//       // Fetch rates for all hardcoded currencies from USD
//       const ratesResponse = await this.getLatestRatesFromUSD(this.hardcodedSymbols.join(','));

//       if (!ratesResponse.success || !ratesResponse.importantRates) {
//         throw new Error('Failed to fetch exchange rates');
//       }

//       const rates = ratesResponse.importantRates;

//       // If base is USD, return the target rate directly
//       if (base === 'USD') {
//         return rates[target];
//       }

//       // If target is USD, return the inverse of the base rate
//       if (target === 'USD') {
//         return 1 / rates[base];
//       }

//       // Calculate intra-currency exchange rate using USD as the base
//       const baseToUSD = rates[base]; // 1 USD = X base
//       const targetToUSD = rates[target]; // 1 USD = Y target

//       // Exchange rate: (1 USD / X base) * (Y target / 1 USD) = Y / X
//       return targetToUSD / baseToUSD;
//     } catch (error) {
//       console.error('Error fetching exchange rate:', error);
//       throw new Error('Failed to fetch exchange rate');
//     }
//   }

//   /**
//    * Swap balances between walletBalance and cardBalance.
//    * @param account - The account object fetched by the controller.
//    * @param user - The user object fetched by the controller.
//    * @param sourceCurrency - The source currency (e.g., 'NGN').
//    * @param targetCurrency - The target currency (e.g., 'SLL').
//    * @param amount - The amount to swap.
//    */
//   async swapBalances(
//     account: any,
//     user: any,
//     sourceCurrency: string,
//     targetCurrency: string,
//     amount: number
//   ): Promise<{
//     success: boolean;
//     message: string;
//     data?: {
//       sourceBalance: number;
//       targetBalance: number;
//       transactionId: string;
//       exchangeRate: number;
//       receipt: string;
//     };
//   }> {
//     try {
//       // Find the source balance
//       const sourceBalance = account.balance.find((b: { currency: string }) => b.currency === sourceCurrency);
//       if (!sourceBalance || sourceBalance.walletBalance < amount) {
//         return { success: false, message: 'Insufficient balance in source wallet' };
//       }

//       // Get the exchange rate
//       const exchangeRate = await this.getExchangeRate(sourceCurrency, targetCurrency);

//       // Calculate the target amount
//       const targetAmount = amount * exchangeRate;

//       // Find the target balance
//       const targetBalance = account.balance.find((b: { currency: string }) => b.currency === targetCurrency);
//       if (!targetBalance) {
//         return { success: false, message: 'Target currency not supported' };
//       }

//       // Update balances
//       sourceBalance.walletBalance -= amount;
//       targetBalance.walletBalance += targetAmount;

//       // Generate a timestamp
//       const timestamp = new Date().toISOString().replace(/[-:T.Z]/g, '').slice(0, 14); // YYYYMMDDHHmmss

//       // Generate a transaction ID
//       const transactionId = generateTransactionId(`${account.accountNumber}-${account.accountNumber}-${timestamp}`);

//       // Create a receipt
//       const receipt = createTransactionReceipt('SANGA', 'SWAP', timestamp, transactionId);

//       // Log the transaction
//       const transaction = new Transaction({
//         transactionId: receipt,
//         type: 'SWAP',
//         title: `Swap from ${sourceCurrency} to ${targetCurrency}`,
//         amount,
//         sender: {
//           userId: new mongoose.Types.ObjectId(user._id),
//           name: user.full_legal_name || user.username,
//           accountNumber: account.accountNumber,
//           username: user.username,
//         },
//         recipient: {
//           userId: new mongoose.Types.ObjectId(user._id),
//           name: user.full_legal_name || user.username,
//           accountNumber: account.accountNumber,
//           username: user.username,
//           email: user.email,
//         },
//         predefinedDescription: `Swap of ${amount} ${sourceCurrency} to ${targetAmount} ${targetCurrency}`,
//         status: 'SUCCESS',
//         timestamp: new Date(),
//         receipt,
//       });

//       // Save the transaction
//       await transaction.save();

//       // Add the transaction to the account
//       account.transactions.push(transaction._id);

//       // Save the updated account
//       await account.save();

//       return {
//         success: true,
//         message: 'SWAP_COMPLETED_SUCCESSFULLY',
//         data: {
//           sourceBalance: sourceBalance.walletBalance,
//           targetBalance: targetBalance.walletBalance,
//           transactionId,
//           exchangeRate,
//           receipt,
//         },
//       };
//     } catch (error) {
//       console.error('Error swapping balances:', error);
//       return { success: false, message: 'Failed to swap balances' };
//     }
//   }
// }



import axios from 'axios';
import dotenv from 'dotenv';
import { Account } from '../models/accountnumber.model';
import mongoose from 'mongoose';
import { createTransactionReceipt, generateTransactionId } from './accountnumber.service';
import { Transaction } from '../models/transaction.model';

dotenv.config();

export class CurrencyConversionService {
  private readonly apiKey: string;
  private readonly baseUrl: string;
  private readonly hardcodedSymbols: string[] = ['NGN', 'SLL', 'LRD', 'GNF']; 

  constructor() {
    this.apiKey = process.env.FIXER_API_KEY!;
    this.baseUrl = 'https://data.fixer.io/api';
  }

  /**
   * Get the latest exchange rates from USD to specified currencies.
   * @param symbols - Comma-separated list of currency codes to fetch rates for (e.g., 'GBP,JPY,EUR').
   * @param includeAllRates - Optional flag to include all rates in the response.
   */
  async getLatestRatesFromUSD(
    symbols: string,
    includeAllRates: boolean = false
  ): Promise<{
    success: boolean;
    timestamp: number;
    base: string;
    date: string;
    importantRates: { [key: string]: number };
    allRates?: { [key: string]: number };
  }> {
    try {
      const params: any = {
        access_key: this.apiKey,
        base: 'USD', 
      };

      const response = await axios.get(`${this.baseUrl}/latest`, { params });

      if (!response.data || !response.data.success || !response.data.rates) {
        throw new Error('Failed to fetch latest rates');
      }

      const importantRates: { [key: string]: number } = {};
      const symbolList = symbols.split(',');
      for (const symbol of symbolList) {
        if (response.data.rates[symbol]) {
          importantRates[symbol] = response.data.rates[symbol];
        }
      }

      const result: {
        success: boolean;
        timestamp: number;
        base: string;
        date: string;
        importantRates: { [key: string]: number };
        allRates?: { [key: string]: number };
      } = {
        success: true,
        timestamp: response.data.timestamp,
        base: response.data.base,
        date: response.data.date,
        importantRates,
      };

      if (includeAllRates) {
        result.allRates = response.data.rates;
      }

      return result;
    } catch (error) {
      console.error('Error fetching latest rates:', error);
      throw new Error('Failed to fetch latest rates');
    }
  }

  /**
   * Get the exchange rate between two currencies.
   * @param base - The base currency (e.g., 'NGN').
   * @param target - The target currency (e.g., 'SLL').
   */
  async getExchangeRate(base: string, target: string): Promise<number> {
    try {
      const ratesResponse = await this.getLatestRatesFromUSD(this.hardcodedSymbols.join(','));

      if (!ratesResponse.success || !ratesResponse.importantRates) {
        throw new Error('Failed to fetch exchange rates');
      }

      const rates = ratesResponse.importantRates;
      if (base === 'USD') {
        return rates[target];
      }

      if (target === 'USD') {
        return 1 / rates[base];
      }

      // Calculate intra-currency exchange rate using USD as the base
      const baseToUSD = rates[base]; // 1 USD = X base
      const targetToUSD = rates[target]; // 1 USD = Y target

      // Exchange rate: (1 USD / X base) * (Y target / 1 USD) = Y / X
      return targetToUSD / baseToUSD;
    } catch (error) {
      console.error('Error fetching exchange rate:', error);
      throw new Error('Failed to fetch exchange rate');
    }
  }

  /**
   * Swap balances between walletBalance and cardBalance.
   * @param account - The account object fetched by the controller.
   * @param user - The user object fetched by the controller.
   * @param sourceCurrency - The source currency (e.g., 'NGN').
   * @param targetCurrency - The target currency (e.g., 'SLL').
   * @param amount - The amount to swap.
   */
  async swapBalances(
    account: any,
    user: any,
    sourceCurrency: string,
    targetCurrency: string,
    amount: number
  ): Promise<{
    success: boolean;
    message: string;
    data?: {
      sourceBalance: number;
      targetBalance: number;
      transactionId: string;
      exchangeRate: number;
      receipt: string;
    };
  }> {
    try {
      const sourceBalance = account.balance.find((b: { currency: string }) => b.currency === sourceCurrency);
      if (!sourceBalance || sourceBalance.walletBalance < amount) {
        return { success: false, message: 'Insufficient balance in source wallet' };
      }

      const exchangeRate = await this.getExchangeRate(sourceCurrency, targetCurrency);

      const targetAmount = amount * exchangeRate;

      const targetBalance = account.balance.find((b: { currency: string }) => b.currency === targetCurrency);
      if (!targetBalance) {
        return { success: false, message: 'Target currency not supported' };
      }

      sourceBalance.walletBalance -= amount;
      targetBalance.walletBalance += targetAmount;

      const timestamp = new Date().toISOString().replace(/[-:T.Z]/g, '').slice(0, 14); // YYYYMMDDHHmmss

      const transactionId = generateTransactionId(`${account.accountNumber}-${account.accountNumber}-${timestamp}`);
      const receipt = createTransactionReceipt('SANGA', 'SWAP', timestamp, transactionId);

      const transaction = new Transaction({
        transactionId: receipt,
        type: 'SWAP',
        title: `Swap from ${sourceCurrency} to ${targetCurrency}`,
        amount,
        sender: {
          userId: new mongoose.Types.ObjectId(user._id),
          name: user.full_legal_name || user.username,
          accountNumber: account.accountNumber,
          username: user.username,
        },
        recipient: {
          userId: new mongoose.Types.ObjectId(user._id),
          name: user.full_legal_name || user.username,
          accountNumber: account.accountNumber,
          username: user.username,
          email: user.email,
        },
        predefinedDescription: `Swap of ${amount} ${sourceCurrency} to ${targetAmount} ${targetCurrency}`,
        status: 'SUCCESS',
        timestamp: new Date(),
        receipt,
      });

      await transaction.save();

      account.transactions.push(transaction._id);

      await account.save();

      return {
        success: true,
        message: 'SWAP_COMPLETED_SUCCESSFULLY',
        data: {
          sourceBalance: sourceBalance.walletBalance,
          targetBalance: targetBalance.walletBalance,
          transactionId,
          exchangeRate,
          receipt,
        },
      };
    } catch (error) {
      console.error('Error swapping balances:', error);
      return { success: false, message: 'Failed to swap balances' };
    }
  }
}