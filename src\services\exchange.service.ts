import axios from 'axios';
import dotenv from 'dotenv';
import { Account } from '../models/accountnumber.model';
import mongoose from 'mongoose';
import { createTransactionReceipt, generateTransactionId } from './accountnumber.service';
import { Transaction } from '../models/transaction.model';

dotenv.config();

export class CurrencyConversionService {
  private readonly apiKey: string;
  private readonly baseUrl: string;
  private readonly hardcodedSymbols: string[] = ['NGN', 'SLL', 'LRD', 'GNF']; 

  constructor() {
    this.apiKey = process.env.FIXER_API_KEY!;
    this.baseUrl = 'https://data.fixer.io/api';
  }


  async getLatestRatesFromUSD(
    symbols: string,
    includeAllRates: boolean = false
  ): Promise<{
    success: boolean;
    timestamp: number;
    base: string;
    date: string;
    importantRates: { [key: string]: number };
    allRates?: { [key: string]: number };
  }> {
    try {
      const params: any = {
        access_key: this.apiKey,
        base: 'USD', 
      };

      const response = await axios.get(`${this.baseUrl}/latest`, { params });

      if (!response.data || !response.data.success || !response.data.rates) {
        throw new Error('Failed to fetch latest rates');
      }

      const importantRates: { [key: string]: number } = {};
      const symbolList = symbols.split(',');
      for (const symbol of symbolList) {
        if (response.data.rates[symbol]) {
          importantRates[symbol] = response.data.rates[symbol];
        }
      }

      const result: {
        success: boolean;
        timestamp: number;
        base: string;
        date: string;
        importantRates: { [key: string]: number };
        allRates?: { [key: string]: number };
      } = {
        success: true,
        timestamp: response.data.timestamp,
        base: response.data.base,
        date: response.data.date,
        importantRates,
      };

      if (includeAllRates) {
        result.allRates = response.data.rates;
      }

      return result;
    } catch (error) {
      console.error('Error fetching latest rates:', error);
      throw new Error('Failed to fetch latest rates');
    }
  }


  async getExchangeRate(base: string, target: string): Promise<number> {
    try {
      const ratesResponse = await this.getLatestRatesFromUSD(this.hardcodedSymbols.join(','));

      if (!ratesResponse.success || !ratesResponse.importantRates) {
        throw new Error('Failed to fetch exchange rates');
      }

      const rates = ratesResponse.importantRates;
      if (base === 'USD') {
        return rates[target];
      }

      if (target === 'USD') {
        return 1 / rates[base];
      }

      const baseToUSD = rates[base];
      const targetToUSD = rates[target];

      return targetToUSD / baseToUSD;
    } catch (error) {
      console.error('Error fetching exchange rate:', error);
      throw new Error('Failed to fetch exchange rate');
    }
  }


  async swapBalances(
    account: any,
    user: any,
    sourceCurrency: string,
    targetCurrency: string,
    amount: number
  ): Promise<{
    success: boolean;
    message: string;
    data?: {
      sourceBalance: number;
      targetBalance: number;
      transactionId: string;
      exchangeRate: number;
      receipt: string;
    };
  }> {
    try {
      const sourceBalance = account.balance.find((b: { currency: string }) => b.currency === sourceCurrency);
      if (!sourceBalance || sourceBalance.walletBalance < amount) {
        return { success: false, message: 'Insufficient balance in source wallet' };
      }

      const exchangeRate = await this.getExchangeRate(sourceCurrency, targetCurrency);

      const targetAmount = amount * exchangeRate;

      const targetBalance = account.balance.find((b: { currency: string }) => b.currency === targetCurrency);
      if (!targetBalance) {
        return { success: false, message: 'Target currency not supported' };
      }

      sourceBalance.walletBalance -= amount;
      targetBalance.walletBalance += targetAmount;

      const timestamp = new Date().toISOString().replace(/[-:T.Z]/g, '').slice(0, 14);

      const transactionId = generateTransactionId(`${account.accountNumber}-${account.accountNumber}-${timestamp}`);
      const receipt = createTransactionReceipt('SANGA', 'SWAP', timestamp, transactionId);

      const transaction = new Transaction({
        transactionId: receipt,
        type: 'SWAP',
        title: `Swap from ${sourceCurrency} to ${targetCurrency}`,
        amount,
        sender: {
          userId: new mongoose.Types.ObjectId(user._id),
          name: user.full_legal_name || user.username,
          accountNumber: account.accountNumber,
          username: user.username,
        },
        recipient: {
          userId: new mongoose.Types.ObjectId(user._id),
          name: user.full_legal_name || user.username,
          accountNumber: account.accountNumber,
          username: user.username,
          email: user.email,
        },
        predefinedDescription: `Swap of ${amount} ${sourceCurrency} to ${targetAmount} ${targetCurrency}`,
        status: 'SUCCESS',
        timestamp: new Date(),
        receipt,
      });

      await transaction.save();

      account.transactions.push(transaction._id);

      await account.save();

      return {
        success: true,
        message: 'SWAP_COMPLETED_SUCCESSFULLY',
        data: {
          sourceBalance: sourceBalance.walletBalance,
          targetBalance: targetBalance.walletBalance,
          transactionId,
          exchangeRate,
          receipt,
        },
      };
    } catch (error) {
      console.error('Error swapping balances:', error);
      return { success: false, message: 'Failed to swap balances' };
    }
  }
}