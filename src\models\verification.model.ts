import mongoose, { Schema, Document } from 'mongoose';

export interface IVerification extends Document {
  userId: mongoose.Types.ObjectId;
  verificationCode: string;
  verificationCodeExpiry: Date;
  purpose: 'PAYMENT' | 'LOGIN' | 'RESET_PASSWORD';
  isUsed: boolean;
}

const VerificationSchema = new Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  verificationCode: {
    type: String,
    required: true
  },
  verificationCodeExpiry: {
    type: Date,
    required: true
  },
  purpose: {
    type: String,
    enum: ['PAYMENT', 'LOGIN', 'RESET_PASSWORD'],
    required: true
  },
  isUsed: {
    type: Boolean,
    default: false
  }
}, { timestamps: true });

export default mongoose.model<IVerification>('Verification', VerificationSchema);