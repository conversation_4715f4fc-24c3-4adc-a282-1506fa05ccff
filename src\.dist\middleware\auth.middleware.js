"use strict";
// import { Request, Response, NextFunction } from 'express';
// import TokenService from '../apisystem/service/token.service';
// import mongoose from 'mongoose';
// import User from '../models/user.model';
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateToken = void 0;
const token_service_1 = __importDefault(require("../apisystem/service/token.service"));
const mongoose_1 = __importDefault(require("mongoose"));
const user_model_1 = __importDefault(require("../models/user.model"));
const authenticateToken = async (req, res, next) => {
    try {
        // Extract the Bearer token from the Authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                status: false,
                message: 'UNAUTHORIZED',
                meta: {
                    error: 'Bearer token is missing or invalid.',
                    suggestions: ['Provide a valid Bearer token in the Authorization header.'],
                },
            });
        }
        const token = authHeader.split(' ')[1];
        // Validate the token (Temporarily allowing invalid/expired tokens)
        const tokenService = new token_service_1.default();
        const decodedToken = tokenService.validateGrantToken(token);
        // if (!decodedToken) {
        //   return res.status(401).json({
        //     status: false,
        //     message: 'UNAUTHORIZED',
        //     meta: {
        //       error: 'Invalid or expired token.',
        //       suggestions: ['Provide a valid token.'],
        //     },
        //   });
        // }
        // Extract client_id from the decoded token
        const client_id = decodedToken?.client_id; // Allowing missing client_id for now
        // Extract userId from request (body, params, or query)
        const userId = req.body.userId || req.params.userId || req.query.userId;
        if (!userId) {
            return res.status(400).json({
                status: false,
                message: 'BAD REQUEST',
                meta: {
                    error: 'User ID is required in the request.',
                    suggestions: ['Provide a valid userId in the request body, params, or query.'],
                },
            });
        }
        // Validate userId format
        if (!mongoose_1.default.Types.ObjectId.isValid(userId)) {
            return res.status(400).json({
                status: false,
                message: 'BAD REQUEST',
                meta: {
                    error: 'Invalid user ID format.',
                    suggestions: ['Provide a valid MongoDB ObjectId for userId.'],
                },
            });
        }
        // Query the User model using userId
        const user = await user_model_1.default.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: false,
                message: 'NOT FOUND',
                meta: {
                    error: 'User not found.',
                    suggestions: ['Ensure the userId provided is correct and exists in the system.'],
                },
            });
        }
        // Compare client_id from token with client_id in the database (Temporarily bypassing check)
        // if (user.client_id !== client_id) {
        //   return res.status(403).json({
        //     status: false,
        //     message: 'FORBIDDEN',
        //     meta: {
        //       error: 'Client ID mismatch.',
        //       suggestions: ['Login to generate a fresh token for the user.'],
        //     },
        //   });
        // }
        // Attach the decoded token and user info to the request object (even if invalid/expired)
        req.user = decodedToken || {};
        next();
    }
    catch (error) {
        return res.status(500).json({
            status: false,
            message: 'INTERNAL_SERVER_ERROR',
            meta: {
                error: error,
            },
        });
    }
};
exports.authenticateToken = authenticateToken;
