import { Request, Response } from 'express';
import mongoose from 'mongoose';
import User from '../models/user.model';
import { Account } from '../models/accountnumber.model';
import { Transaction } from '../models/transaction.model';
import { ReloadlyService } from '../services/airtime.service';
import crypto from 'crypto';

const SUPPORTED_CURRENCIES = ['USD', 'LRD', 'SLL', 'GNF', 'NGN'];

export const purchaseAirtime = async (req: Request, res: Response): Promise<any> => {
    const { userId, phoneNumber, countryCode, amount, currency } = req.body;
    const transactionStartTime = new Date();
  
    // Validate userId
    if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_USER_ID',
        timestamp: new Date().toISOString(),
        meta: {
          error: 'The provided userId is missing or not a valid ObjectId.',
          suggestions: ['Provide a valid userId and try again.']
        }
      });
    }
  
    // Validate currency
    if (!currency || !SUPPORTED_CURRENCIES.includes(currency)) {
      return res.status(400).json({
        status: false,
        message: 'INVALID_CURRENCY',
        timestamp: new Date().toISOString(),
        meta: {
          error: 'The provided currency is missing or not supported.',
          suggestions: [`Provide a valid currency: ${SUPPORTED_CURRENCIES.join(', ')}.`]
        }
      });
    }
  
    const session = await mongoose.startSession();
    session.startTransaction();
  
    try {
      // Find the user
      const user = await User.findById(userId).session(session);
      if (!user) {
        await session.abortTransaction();
        return res.status(404).json({
          status: false,
          message: 'USER_NOT_FOUND',
          timestamp: new Date().toISOString(),
          meta: {
            error: 'User with the provided ID does not exist.',
            suggestions: ['Check the user ID and try again.']
          }
        });
      }
  
      // Find the account
      const account = await Account.findOne({ userId }).session(session);
      if (!account) {
        await session.abortTransaction();
        return res.status(404).json({
          status: false,
          message: 'ACCOUNT_NOT_FOUND',
          timestamp: new Date().toISOString(),
          meta: {
            error: 'Account for the provided user does not exist.',
            suggestions: ['Create an account first.']
          }
        });
      }
  
      // Check balance
      const walletBalance = account.balance.find(b => b.currency === currency)?.walletBalance || 0;
      if (walletBalance < amount) {
        await session.abortTransaction();
        return res.status(400).json({
          status: false,
          message: 'INSUFFICIENT_BALANCE',
          timestamp: new Date().toISOString(),
          meta: {
            error: `Not enough funds in the ${currency} wallet.`,
            currentBalance: walletBalance,
            requestedAmount: amount,
            suggestions: ['Deposit funds or choose a smaller amount.']
          }
        });
      }
  
      // Create transaction ID
      const transactionId = `airtime-tx_${crypto.randomBytes(16).toString('hex')}`;
      const createdAt = new Date();
  
      // Create pending transaction record
      const pendingTransaction = await Transaction.create([{
        transactionId,
        type: 'DEBIT',
        title: 'Airtime Purchase',
        amount,
        currency,
        sender: {
          userId: user._id,
          name: user.full_legal_name,
          accountNumber: account.accountNumber,
        },
        recipient: {
          userId: user._id,
          name: user.full_legal_name,
          accountNumber: phoneNumber,
          email: user.email
        },
        predefinedDescription: `Airtime purchase for ${phoneNumber}`,
        status: 'PENDING',
        createdAt,
        updatedAt: createdAt
      }], { session });
  
      // Initiate top-up with Reloadly
      const reloadlyService = new ReloadlyService();
      const topupResult = await reloadlyService.processTopUp(
        phoneNumber,
        countryCode,
        amount,
        { useLocalAmount: true }
      );
  
      const updatedAt = new Date();
      const processingTimeMs = updatedAt.getTime() - transactionStartTime.getTime();
  
      // Update transaction and account balance based on Reloadly response
      if (topupResult.status === 'SUCCESSFUL') {
        // Update transaction status
        await Transaction.findByIdAndUpdate(
          pendingTransaction[0]._id,
          {
            status: 'SUCCESS',
            transactionId: topupResult.transactionId,
            receipt: JSON.stringify({
              operatorId: topupResult.operatorId,
              operatorName: topupResult.operatorName,
              deliveredAmount: topupResult.deliveredAmount,
              deliveredCurrency: topupResult.deliveredAmountCurrencyCode,
              operatorTransactionId: topupResult.operatorTransactionId
            }),
            updatedAt
          },
          { session }
        );
  
        // Deduct from user's wallet
        await Account.findOneAndUpdate(
          { userId, 'balance.currency': currency },
          { $inc: { 'balance.$.walletBalance': -amount } },
          { session }
        );
  
        await session.commitTransaction();
  
        return res.status(200).json({
          status: true,
          message: 'AIRTIME_PURCHASE_SUCCESSFUL',
          timestamp: updatedAt.toISOString(),
          processingTimeMs,
          data: {
            transactionId,
            internalTransactionId: pendingTransaction[0]._id,
            reloadlyTransactionId: topupResult.transactionId,
            status: 'SUCCESS',
            amount,
            currency,
            phoneNumber,
            countryCode,
            operator: {
              id: topupResult.operatorId,
              name: topupResult.operatorName
            },
            deliveredAmount: topupResult.deliveredAmount,
            deliveredCurrency: topupResult.deliveredAmountCurrencyCode,
            timestamps: {
              initiatedAt: createdAt.toISOString(),
              completedAt: updatedAt.toISOString()
            }
          }
        });
      } else {
        // Handle failed transaction
        await Transaction.findByIdAndUpdate(
          pendingTransaction[0]._id,
          {
            status: 'FAILED',
            transactionId: topupResult.transactionId,
            receipt: JSON.stringify({
              failureReason: topupResult.message || 'Unknown reason',
              operatorId: topupResult.operatorId,
              operatorName: topupResult.operatorName
            }),
            updatedAt
          },
          { session }
        );
  
        await session.commitTransaction();
  
        return res.status(400).json({
          status: false,
          message: 'AIRTIME_PURCHASE_FAILED',
          timestamp: updatedAt.toISOString(),
          processingTimeMs,
          data: {
            transactionId,
            internalTransactionId: pendingTransaction[0]._id,
            reloadlyTransactionId: topupResult.transactionId,
            status: 'FAILED',
            amount,
            currency,
            phoneNumber,
            countryCode,
            error: topupResult.message || 'Unknown error',
            timestamps: {
              initiatedAt: createdAt.toISOString(),
              failedAt: updatedAt.toISOString()
            }
          },
          meta: {
            suggestions: [
              'Verify the phone number and try again',
              'Contact support if the problem persists'
            ]
          }
        });
      }
    } catch (error: any) {
      await session.abortTransaction();
      console.error('Airtime purchase error:', error);
      const errorTime = new Date();
      return res.status(500).json({
        status: false,
        message: 'AIRTIME_PURCHASE_ERROR',
        timestamp: errorTime.toISOString(),
        meta: {
          error: error.message,
          suggestions: [
            'Check your input data for errors.',
            'Ensure the payment service is available.',
            'Try again later.'
          ]
        }
      });
    } finally {
      session.endSession();
    }
  };


export const lookupAirtimeOperator = async (req: Request, res: Response): Promise<any> => {
    const { phoneNumber, countryCode } = req.query;
  
    // Validate required parameters
    if (!phoneNumber || !countryCode) {
      return res.status(400).json({
        status: false,
        message: 'MISSING_REQUIRED_FIELDS',
        meta: {
          error: 'Both phoneNumber and countryCode are required',
          suggestions: ['Provide both phoneNumber and countryCode as query parameters']
        }
      });
    }
  
    try {
      const reloadlyService = new ReloadlyService();
      const operatorData = await reloadlyService.detectOperator(
        phoneNumber as string,
        countryCode as string
      );
  
      return res.status(200).json({
        status: true,
        message: 'OPERATOR_DETECTED',
        data: operatorData // Return the complete operator data
      });
  
    } catch (error: any) {
      console.error('Operator lookup error:', error);
      return res.status(500).json({
        status: false,
        message: 'OPERATOR_LOOKUP_FAILED',
        meta: {
          error: error.message,
          suggestions: [
            'Check the phone number and country code format',
            'Ensure the service is available',
            'Try again later'
          ]
        }
      });
    }
  };