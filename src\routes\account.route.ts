import express from 'express';
import { aggregateUserTransactions, createAccountNumber, getUserInfoByAccountNumber, getWalletData, intraTransfer } from '../controllers/accountnumber.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { createCardholder, createCardholderIssueAndRetrieveCard, createPaymentIntent, createStripeAccount, handleStripeWebhook, issueCreditCard, linkCard, retrieveAndDecryptCardDetails, retrieveCreditCard } from '../controllers/stripe.controller';
import { StripeService } from '../services/stripe.service';
import { swap } from '../controllers/exchange.controller';

const stripeService = new StripeService();


const accountRouter = express.Router();

accountRouter.post('/create-account-number/:userId', authenticateToken, createAccountNumber); 
accountRouter.post('/intra-transfer', authenticateToken, intraTransfer); 
accountRouter.get('/fetch-transactions/:userId', authenticateToken, aggregateUserTransactions); 

accountRouter.get('/get-account-name/:accountNumber', getUserInfoByAccountNumber ); 

accountRouter.post('/create-usd-cardholder', authenticateToken, createCardholder); 
accountRouter.post('/create-credit-card', authenticateToken, issueCreditCard); 
accountRouter.post('/create-stripe-account', authenticateToken, createStripeAccount); 
accountRouter.post('/link-card-to-stripe-account', authenticateToken, linkCard); 


accountRouter.get('/fetch-credit-card/:userId', authenticateToken, retrieveCreditCard); 

accountRouter.post('/swap', authenticateToken, swap); 


accountRouter.get('/fetch-wallet-balance/:userId', authenticateToken, getWalletData); 
accountRouter.post('/create-and-fetch-creditcard', authenticateToken, createCardholderIssueAndRetrieveCard); 
accountRouter.get('/fetch-creditcard/:userId/account/:accountNumber', authenticateToken, retrieveAndDecryptCardDetails); 
accountRouter.post('/fund-usd-balance', authenticateToken, createPaymentIntent); 
accountRouter.post('/stripe/webhook', express.raw({ type: 'application/json' }), handleStripeWebhook);









export default accountRouter;
