"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_controller_1 = require("../controllers/user.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const uploader_service_1 = require("../services/uploader.service");
const admin_controller_1 = require("../controllers/admin.controller");
const userRouter = (0, express_1.Router)();
// Profile and basic routes
userRouter.get('/me/:userId', auth_middleware_1.authenticateToken, user_controller_1.getUserDetails);
userRouter.post('/profile/:userId', auth_middleware_1.authenticateToken, user_controller_1.updateProfile);
userRouter.post('/upgrade-to-business/:userId', auth_middleware_1.authenticateToken, admin_controller_1.upgradeAccountToBusiness);
userRouter.post('/submit-business-details/:userId', auth_middleware_1.authenticateToken, user_controller_1.submitBusinessDetails);
// File upload routes using consistent pattern
userRouter.post('/profile-picture/:userId', auth_middleware_1.authenticateToken, uploader_service_1.upload.array('profilePicture', 1), user_controller_1.updateProfilePicture);
userRouter.post('/kyc/:userId/kyc-documents', auth_middleware_1.authenticateToken, uploader_service_1.upload.array('kyc', 5), user_controller_1.uploadKycDocuments);
userRouter.post('/:userId/pin/set', auth_middleware_1.authenticateToken, user_controller_1.setPin);
userRouter.post('/:userId/pin/verify', auth_middleware_1.authenticateToken, user_controller_1.verifyPin);
userRouter.post('/:userId/pin/verify-payment-code', auth_middleware_1.authenticateToken, user_controller_1.verifyPaymentCode);
userRouter.put('/:userId/pin/update', auth_middleware_1.authenticateToken, user_controller_1.updatePin);
userRouter.delete('/:userId/pin/delete', auth_middleware_1.authenticateToken, user_controller_1.deletePin);
userRouter.post('/:userId/addbeneficiaries', auth_middleware_1.authenticateToken, user_controller_1.addBeneficiary);
userRouter.get('/:userId/getbeneficiaries', auth_middleware_1.authenticateToken, user_controller_1.getBeneficiaries);
exports.default = userRouter;
