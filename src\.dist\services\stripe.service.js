"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripeService = void 0;
const stripe_1 = __importDefault(require("stripe"));
const dotenv_1 = __importDefault(require("dotenv"));
const crypto_1 = __importDefault(require("crypto"));
dotenv_1.default.config();
class StripeService {
    constructor() {
        // Initialize Stripe with the secret key from environment variables
        this.stripe = new stripe_1.default(process.env.STRIPE_SECRET_KEY, {
            apiVersion: '2024-12-18.acacia', // Use the appropriate Stripe API version
        });
    }
    /**
     * Create a cardholder in Stripe
     */
    async createCardholder(userDetails) {
        try {
            // Create the cardholder in Stripe
            const cardholder = await this.stripe.issuing.cardholders.create({
                name: userDetails.name, // Required
                email: userDetails.email, // Optional
                phone_number: userDetails.phone_number, // Optional
                billing: {
                    address: {
                        line1: userDetails.billing_address.line1, // Required
                        city: userDetails.billing_address.city, // Required
                        postal_code: userDetails.billing_address.postal_code, // Required
                        country: userDetails.billing_address.country, // Required
                        line2: userDetails.billing_address.line2, // Optional
                        state: userDetails.billing_address.state, // Optional
                    },
                },
                metadata: userDetails.metadata, // Optional
                individual: userDetails.individual
                    ? {
                        first_name: userDetails.individual.first_name, // Optional
                        last_name: userDetails.individual.last_name, // Optional
                        dob: {
                            day: userDetails.individual.dob.day, // Required
                            month: userDetails.individual.dob.month, // Required
                            year: userDetails.individual.dob.year, // Required
                        },
                        card_issuing: userDetails.individual.card_issuing
                            ? {
                                user_terms_acceptance: userDetails.individual.card_issuing.user_terms_acceptance
                                    ? {
                                        date: userDetails.individual.card_issuing.user_terms_acceptance.date, // Optional
                                        ip: userDetails.individual.card_issuing.user_terms_acceptance.ip, // Optional
                                        user_agent: userDetails.individual.card_issuing.user_terms_acceptance.user_agent, // Optional
                                    }
                                    : undefined,
                            }
                            : undefined,
                    }
                    : undefined,
                status: 'active', // Default status
                type: 'individual', // Default type
            });
            return cardholder;
        }
        catch (error) {
            console.error('Error creating cardholder:', error);
            throw error;
        }
    }
    /**
   * Create a Stripe Connected Account for a user
   */
    async createConnectedAccount(userDetails) {
        try {
            const account = await this.stripe.accounts.create({
                type: 'custom',
                country: userDetails.country,
                email: userDetails.email,
                business_type: userDetails.businessType || 'individual',
                individual: userDetails.individual
                    ? {
                        first_name: userDetails.individual.first_name,
                        last_name: userDetails.individual.last_name,
                        dob: {
                            day: userDetails.individual.dob.day,
                            month: userDetails.individual.dob.month,
                            year: userDetails.individual.dob.year,
                        },
                    }
                    : undefined,
                tos_acceptance: {
                    date: userDetails.tosAcceptance.date,
                    ip: userDetails.tosAcceptance.ip,
                },
                capabilities: {
                    card_payments: { requested: true },
                    transfers: { requested: true },
                },
            });
            return account;
        }
        catch (error) {
            console.error('Error creating connected account:', error);
            throw error;
        }
    }
    /**
   * Link a card to a user's Stripe Connected Account using a token
   */
    async linkCardToAccount(stripeAccountId, token) {
        try {
            // Attach the card token to the user's Stripe Connected Account
            const externalAccount = await this.stripe.accounts.createExternalAccount(stripeAccountId, {
                external_account: token, // Token created on the frontend
            });
            return externalAccount;
        }
        catch (error) {
            console.error('Error linking card:', error);
            throw error;
        }
    }
    /**
   * Withdraw funds to a user's linked card
   */
    async withdrawToCard(stripeAccountId, amount, currency = 'usd') {
        try {
            // Create a payout to the user's linked card
            const payout = await this.stripe.payouts.create({
                amount: amount * 100, // Amount in cents
                currency, // Currency
                destination: stripeAccountId, // Withdraw to the linked card
            }, {
                stripeAccount: stripeAccountId, // Use the user's Stripe Connected Account
            });
            return payout;
        }
        catch (error) {
            console.error('Error withdrawing funds:', error);
            throw error;
        }
    }
    /**
     * Encrypt a PIN for card issuance
     */
    encryptPin(pin) {
        // Retrieve Stripe's public key from environment variables
        const stripePublicKey = process.env.STRIPE_PUBLIC_KEY;
        // Ensure the public key is in PEM format
        const publicKey = `-----BEGIN PUBLIC KEY-----\n${stripePublicKey}\n-----END PUBLIC KEY-----`;
        // Encrypt the PIN using RSA-OAEP with SHA-256
        const buffer = Buffer.from(pin);
        const encrypted = crypto_1.default.publicEncrypt({
            key: publicKey,
            padding: crypto_1.default.constants.RSA_PKCS1_OAEP_PADDING,
            oaepHash: 'sha256',
        }, buffer);
        // Return the encrypted PIN as a base64-encoded string
        return encrypted.toString('base64');
    }
    /**
     * Issue a physical or virtual card
     */
    async issueCard(cardholderId, type, metadata, pin, shippingAddress, spendingControls, replacementFor, replacementReason, personalizationDesign, secondLine) {
        try {
            // Encrypt the PIN if provided
            const encryptedPin = pin ? this.encryptPin(pin) : undefined;
            // Create the card in Stripe
            const card = await this.stripe.issuing.cards.create({
                cardholder: cardholderId, // ID of the cardholder
                currency: 'usd', // Currency for the card
                type: type, // Type of card (virtual or physical)
                status: 'active', // Default status
                metadata: metadata, // Optional metadata
                pin: encryptedPin ? { encrypted_number: encryptedPin } : undefined, // Encrypted PIN
                shipping: shippingAddress, // Optional shipping address
                spending_controls: spendingControls, // Optional spending controls
                replacement_for: replacementFor, // Optional replacement card ID
                replacement_reason: replacementReason, // Optional replacement reason
                personalization_design: personalizationDesign, // Optional personalization design
                second_line: secondLine, // Optional second line on the card
            });
            return card;
        }
        catch (error) {
            console.error('Error issuing card:', error);
            throw error;
        }
    }
    /**
     * Fetch the details of a specific card
     */
    async getCardDetails(cardId) {
        try {
            // Retrieve the card details from Stripe with expanded fields
            const card = await this.stripe.issuing.cards.retrieve(cardId, {
                expand: ['number', 'cvc'], // Expand the number and cvc fields
            });
            return card;
        }
        catch (error) {
            console.error('Error fetching card details:', error);
            throw error;
        }
    }
    /**
     * Create a PaymentMethod using card details
     */
    async createPaymentMethod(cardDetails) {
        try {
            const paymentMethod = await this.stripe.paymentMethods.create({
                type: "card",
                card: {
                    number: cardDetails.cardNumber,
                    exp_month: cardDetails.expMonth,
                    exp_year: cardDetails.expYear,
                    cvc: cardDetails.cvc,
                },
            });
            return paymentMethod.id;
        }
        catch (error) {
            console.error("Error creating PaymentMethod:", error);
            throw error;
        }
    }
    /**
     * Create a PaymentIntent to charge the user's card
     */
    async createPaymentIntent(amount, currency = "usd", description, metadata, paymentMethodId, billingDetails) {
        try {
            const paymentIntent = await this.stripe.paymentIntents.create({
                amount,
                currency,
                description,
                metadata, // Optional metadata (custom key-value pairs)
                payment_method: paymentMethodId,
                confirm: true, // Automatically confirm the payment
                payment_method_types: ["card"],
            });
            return paymentIntent;
        }
        catch (error) {
            console.error("Error creating PaymentIntent:", error);
            throw error;
        }
    }
    /**
     * Get the Stripe Financial Account (For Issuing)
     */
    async getFinancialAccount() {
        const financialAccounts = await this.stripe.treasury.financialAccounts.list({
            limit: 1,
        });
        if (!financialAccounts.data.length) {
            throw new Error("No financial account configured for Stripe Issuing");
        }
        return financialAccounts.data[0];
    }
    /**
     * Transfer Funds to the User’s Connected Account or Financial Account
     */
    async transferFunds(amount, currency, destinationAccountId, userId, paymentIntentId) {
        try {
            const transfer = await this.stripe.transfers.create({
                amount,
                currency,
                destination: destinationAccountId, // Send to user's connected account
                description: `Transfer to user ${userId}`,
                metadata: { userId, paymentIntentId },
            });
            return transfer;
        }
        catch (error) {
            console.error("Error transferring funds:", error);
            throw error;
        }
    }
    /**
     * Handle Stripe webhook events
     */
    async handleWebhookEvent(rawBody, signatureHeader) {
        try {
            const event = this.stripe.webhooks.constructEvent(rawBody, signatureHeader, process.env.STRIPE_WEBHOOK_SECRET);
            // Handle specific event types
            switch (event.type) {
                case 'payment_intent.succeeded':
                    const paymentIntent = event.data.object;
                    console.log('PaymentIntent succeeded:', paymentIntent.id);
                    break;
                case 'issuing_card.created':
                    const card = event.data.object;
                    console.log('New card issued:', card.id);
                    break;
                case 'charge.succeeded':
                    const charge = event.data.object;
                    console.log('Charge succeeded:', charge.id);
                    break;
                default:
                    console.log(`Unhandled event type: ${event.type}`);
            }
            return event;
        }
        catch (error) {
            console.error('Webhook Error:', error);
            throw error;
        }
    }
}
exports.StripeService = StripeService;
